import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';

interface PolicyAgreementComponentProps {
  onAgreementChange: (agreed: boolean) => void;
  isRequired?: boolean;
  disabled?: boolean;
  style?: any;
}

const LEGAL_DECLARATION_TEXT = `Declaration and Terms of Submission

I hereby declare that the information provided in this form is true, complete, and correct to the best of my knowledge. I understand that any false or misleading information may result in the rejection of this application, cancellation of the policy, or denial of any claims made.

I acknowledge that:
• This form, once submitted, constitutes a legal and binding declaration.
• This document may be used as admissible evidence in a court of law or arbitration proceedings, should any dispute arise.
• I consent to the insurance company and/or its appointed representatives verifying any information provided herein.
• I accept that the insurer reserves the right to request additional documentation or clarification as deemed necessary.
• I understand that cover shall only be effective upon acceptance by the insurer and subject to the terms and conditions of the policy issued.
• I indemnify the insurer and its intermediaries against any loss or liability arising from inaccuracies in the information provided by me.

By submitting this form, I confirm that I have read, understood, and agree to be bound by the terms, conditions, and privacy policies of the insurer.`;

export default function PolicyAgreementComponent({
  onAgreementChange,
  isRequired = true,
  disabled = false,
  style,
}: PolicyAgreementComponentProps) {
  const [isAgreed, setIsAgreed] = useState(false);
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);

  const handleAgreementToggle = () => {
    if (disabled) return;
    
    const newAgreedState = !isAgreed;
    setIsAgreed(newAgreedState);
    onAgreementChange(newAgreedState);
  };

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const paddingToBottom = 20;
    
    // Check if user has scrolled to the bottom
    if (layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom) {
      setHasScrolledToBottom(true);
    }
  };

  const isCheckboxEnabled = hasScrolledToBottom && !disabled;

  return (
    <View style={[styles.container, style]}>
      {/* Header */}
      <View style={styles.header}>
        <Ionicons name="document-text-outline" size={24} color={Colors.primary} />
        <Text style={styles.headerText}>Legal Declaration</Text>
        {isRequired && <Text style={styles.requiredIndicator}>*</Text>}
      </View>

      {/* Scrollable Declaration Text */}
      <View style={styles.declarationContainer}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={true}
          nestedScrollEnabled={true}
        >
          <Text style={styles.declarationText}>{LEGAL_DECLARATION_TEXT}</Text>
        </ScrollView>
        
        {/* Scroll indicator */}
        {!hasScrolledToBottom && (
          <View style={styles.scrollIndicator}>
            <Text style={styles.scrollIndicatorText}>
              Please scroll to read the complete declaration
            </Text>
            <Ionicons name="chevron-down" size={16} color={Colors.textSecondary} />
          </View>
        )}
      </View>

      {/* Agreement Checkbox */}
      <TouchableOpacity
        style={[
          styles.checkboxContainer,
          !isCheckboxEnabled && styles.checkboxContainerDisabled,
        ]}
        onPress={handleAgreementToggle}
        disabled={!isCheckboxEnabled}
        accessibilityRole="checkbox"
        accessibilityState={{ checked: isAgreed }}
        accessibilityLabel="I agree to the declaration and terms of submission"
      >
        <View style={[
          styles.checkbox,
          isAgreed && styles.checkboxChecked,
          !isCheckboxEnabled && styles.checkboxDisabled,
        ]}>
          {isAgreed && (
            <Ionicons name="checkmark" size={16} color={Colors.white} />
          )}
        </View>
        <Text style={[
          styles.checkboxText,
          !isCheckboxEnabled && styles.checkboxTextDisabled,
        ]}>
          I have read, understood, and agree to the declaration and terms above
          {isRequired && <Text style={styles.requiredIndicator}> *</Text>}
        </Text>
      </TouchableOpacity>

      {/* Help Text */}
      {!hasScrolledToBottom && (
        <Text style={styles.helpText}>
          You must read the complete declaration before you can agree to the terms.
        </Text>
      )}
    </View>
  );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
    flex: 1,
  },
  requiredIndicator: {
    color: Colors.error,
    fontSize: 18,
    fontWeight: 'bold',
  },
  declarationContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  scrollView: {
    maxHeight: 200,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    backgroundColor: Colors.inputBackground,
  },
  scrollContent: {
    padding: 16,
  },
  declarationText: {
    fontSize: 14,
    lineHeight: 20,
    color: Colors.text,
    textAlign: 'justify',
  },
  scrollIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  scrollIndicatorText: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginRight: 4,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  checkboxContainerDisabled: {
    opacity: 0.5,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.border,
    backgroundColor: Colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  checkboxChecked: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  checkboxDisabled: {
    backgroundColor: Colors.disabled,
    borderColor: Colors.disabled,
  },
  checkboxText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    color: Colors.text,
  },
  checkboxTextDisabled: {
    color: Colors.textSecondary,
  },
  helpText: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});
