import React, { useState, useEffect, useCallback, memo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Modal, ScrollView, Alert } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { FileText, Upload, Check, AlertCircle, X, Eye } from 'lucide-react-native';
import { QuoteDocument } from '@/types/quote.types';
import Animated, { FadeInDown } from 'react-native-reanimated';
import UltimateDocumentUploader from '@/components/documents/UltimateDocumentUploader';
import { Document } from '@/components/documents/types';
import { useVerifiedDocuments } from '@/context/OptimizedUploadContext';
import { showToast } from '@/utils/toast';
import { router } from 'expo-router';

interface RequiredDocumentsListProps {
  documents: QuoteDocument[];
  onUpload: (documentType: string) => void;
  onView?: (documentId: string) => void;
}

// Memoize the component to prevent unnecessary re-renders
const RequiredDocumentsList: React.FC<RequiredDocumentsListProps> = memo(({
  documents,
  onUpload,
  onView,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { documents: verifiedDocuments, addDocument } = useVerifiedDocuments();

  // Helper function to get verified document by type
  const getVerifiedDocumentByType = (documentType: string) => {
    return verifiedDocuments.find(doc => doc.name === documentType) || null;
  };

  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState<string | null>(null);

  // Handle document upload - memoize with useCallback to prevent re-renders
  const handleDocumentUploadClick = useCallback((documentType: string) => {
    // Check if there's already a verified document of this type
    const verifiedDocument = getVerifiedDocumentByType(documentType);

    if (verifiedDocument) {
      // If a verified document exists, use it instead of asking for a new upload
      console.log('Using existing verified document:', verifiedDocument.id);

      // Call the onUpload callback with the document type
      onUpload(documentType);

      // Show success message
      showToast(
        'success',
        'Document Reused',
        `Document has been automatically added from your verified documents`,
        { visibilityTime: 3000 }
      );
    } else {
      // If no verified document exists, ask for confirmation to upload directly
      Alert.alert(
        'Upload Document',
        'Would you like to upload this document now?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Upload',
            onPress: () => {
              // Set the selected document type and show the upload modal
              setSelectedDocumentType(documentType);
              setUploadModalVisible(true);
            },
          },
        ]
      );
    }
  }, [getVerifiedDocumentByType, onUpload]);

  // Handle document uploaded callback
  const handleDocumentUploaded = useCallback((document: Document) => {
    // Add document to verified documents
    addDocument(document);

    if (selectedDocumentType) {
      onUpload(selectedDocumentType);
      setUploadModalVisible(false);

      // Show success message
      showToast(
        'success',
        'Document Uploaded',
        `Document has been uploaded successfully`,
        { visibilityTime: 3000 }
      );
    }
  }, [selectedDocumentType, onUpload, addDocument]);

  // Close modal handler
  const handleCloseModal = useCallback(() => {
    setUploadModalVisible(false);
  }, []);

  const styles = React.useMemo(() => StyleSheet.create({
    container: {
      marginBottom: spacing.lg,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    documentItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      borderLeftWidth: 4,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    documentIcon: {
      marginRight: spacing.md,
      flexShrink: 0, // Prevent icon from shrinking
    },
    documentContent: {
      flex: 1,
      minWidth: 0, // Important for text truncation to work
    },
    documentName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: 4,
      flexShrink: 1, // Allow text to shrink
      flexWrap: 'wrap', // Allow text to wrap
    },
    documentStatus: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: `${colors.primary[500]}15`,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
      marginLeft: spacing.sm,
      flexShrink: 0, // Prevent button from shrinking
    },
    actionButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.primary[500],
      marginLeft: spacing.xs,
    },
    requiredBadge: {
      backgroundColor: colors.error[50],
      paddingHorizontal: spacing.xs,
      paddingVertical: 2,
      borderRadius: borders.radius.sm,
      marginLeft: spacing.xs,
    },
    requiredText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.error[500],
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.md,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    closeButton: {
      padding: spacing.xs,
    },
    modalContent: {
      flex: 1,
      padding: spacing.md,
    },
    warningContainer: {
      backgroundColor: colors.warning[50],
      padding: spacing.md,
      borderRadius: borders.radius.md,
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    warningText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.warning[700],
      flex: 1,
      marginLeft: spacing.sm,
    },
  }), [colors, spacing, typography, borders]);

  // Mock verification state for now (can be enhanced later)
  const documentsInVerification: string[] = [];
  const getDocumentById = (id: string) => verifiedDocuments.find(doc => doc.id === id) || null;

  // Memoize the renderDocumentItem function to prevent re-renders
  const renderDocumentItem = useCallback(({ item, index }: { item: QuoteDocument; index: number }) => {
    const isUploaded = item.uploaded;
    const isInVerification = item.documentId ? documentsInVerification.includes(item.documentId) : false;

    // Get the current document from the context if it exists
    const currentDoc = item.documentId ? getDocumentById(item.documentId) : null;

    // Use the most up-to-date status
    const documentStatus = currentDoc ? currentDoc.status : item.status;
    const documentReason = currentDoc ? currentDoc.reason : item.reason;

    const borderColor = item.required
      ? isUploaded
        ? documentStatus === 'verified'
          ? colors.success[500]
          : documentStatus === 'rejected'
            ? colors.error[500]
            : isInVerification
              ? colors.warning[500]
              : colors.warning[500]
        : colors.error[500]
      : isUploaded
        ? documentStatus === 'verified'
          ? colors.success[500]
          : documentStatus === 'rejected'
            ? colors.error[500]
            : isInVerification
              ? colors.warning[500]
              : colors.warning[500]
        : colors.warning[500];

    let statusColor = colors.textSecondary;
    let statusText = 'Not uploaded';

    if (isUploaded) {
      switch (documentStatus) {
        case 'verified':
          statusColor = colors.success[500];
          statusText = 'Verified';
          break;
        case 'rejected':
          statusColor = colors.error[500];
          statusText = documentReason ? `Rejected: ${documentReason}` : 'Rejected';
          break;
        default:
          statusColor = colors.warning[500];
          statusText = isInVerification ? 'Pending verification' : 'Uploaded';
          break;
      }
    } else {
      statusColor = item.required ? colors.error[500] : colors.warning[500];
      statusText = item.required ? 'Required' : 'Optional';
    }

    return (
      <Animated.View
        entering={FadeInDown.delay(100 + index * 50).springify()}
      >
        <View style={[styles.documentItem, { borderLeftColor: borderColor }]}>
          <FileText
            size={24}
            color={isUploaded ? colors.success[500] : colors.textSecondary}
            style={styles.documentIcon}
          />

          <View style={styles.documentContent}>
            <View style={{ flexDirection: 'row', alignItems: 'flex-start', flexWrap: 'wrap' }}>
              <Text style={styles.documentName} numberOfLines={2} ellipsizeMode="tail">
                {item.name}
              </Text>
              {item.required && (
                <View style={styles.requiredBadge}>
                  <Text style={styles.requiredText}>REQUIRED</Text>
                </View>
              )}
            </View>

            <Text style={[styles.documentStatus, { color: statusColor }]} numberOfLines={1} ellipsizeMode="tail">
              {statusText}
            </Text>
          </View>

          {isUploaded && item.documentId && onView ? (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => onView(item.documentId!)}
            >
              <Eye size={16} color={colors.primary[500]} />
              <Text style={styles.actionButtonText}>View</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[
                styles.actionButton,
                {
                  backgroundColor: isInVerification
                    ? `${colors.textSecondary}15`
                    : documentStatus === 'rejected'
                      ? `${colors.error[500]}15`
                      : `${colors.primary[500]}15`
                }
              ]}
              onPress={() => handleDocumentUploadClick(item.type)}
              disabled={isInVerification}
            >
              <Upload
                size={16}
                color={
                  isInVerification
                    ? colors.textSecondary
                    : documentStatus === 'rejected'
                      ? colors.error[500]
                      : colors.primary[500]
                }
              />
              <Text
                style={[
                  styles.actionButtonText,
                  {
                    color: isInVerification
                      ? colors.textSecondary
                      : documentStatus === 'rejected'
                        ? colors.error[500]
                        : colors.primary[500]
                  }
                ]}
              >
                {isInVerification
                  ? 'Verifying...'
                  : documentStatus === 'rejected'
                    ? 'Re-upload'
                    : 'Upload'
                }
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>
    );
  }, [colors, handleDocumentUploadClick, onView, documentsInVerification, getDocumentById]);

  // Memoize the renderEmptyState function to prevent re-renders
  const renderEmptyState = useCallback(() => (
    <View style={styles.emptyContainer}>
      <FileText size={48} color={colors.textSecondary} />
      <Text style={styles.emptyText}>No documents required for this quote</Text>
    </View>
  ), [colors, styles]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Required Documents</Text>

      {React.useMemo(() => {
        return documents.length > 0 ? (
          <FlatList
            data={documents}
            renderItem={renderDocumentItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        ) : (
          renderEmptyState()
        );
      }, [documents, renderDocumentItem, renderEmptyState])}

      {/* Document Upload Modal */}
      <Modal
        visible={uploadModalVisible}
        animationType="slide"
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Upload Document</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleCloseModal}
            >
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.modalContent}
            // Add these props to improve scrolling performance
            removeClippedSubviews={true}
            showsVerticalScrollIndicator={false}
          >
            {/* Only render when modal is visible to prevent unnecessary renders */}
            {uploadModalVisible && (
              <UltimateDocumentUploader
                preselectedDocumentType={selectedDocumentType || undefined}
                onDocumentUploaded={handleDocumentUploaded}
                maxFileSize={10 * 1024 * 1024}
                allowedFileTypes={['image', 'pdf', 'doc']}
                uploadType="document"
              />
            )}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
});

export default memo(RequiredDocumentsList);
