import { configureStore } from '@reduxjs/toolkit';
import authSlice, {
  login,
  register,
  logout,
  forgotPassword,
  resetPassword,
  initializeAuth,
} from '../../../store/authSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage');

// Mock API service
jest.mock('../../../services/api', () => ({
  apiService: {
    auth: {
      login: jest.fn(),
      register: jest.fn(),
      forgotPassword: jest.fn(),
      resetPassword: jest.fn(),
      getCurrentUser: jest.fn(),
    },
  },
}));

describe('authSlice', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authSlice,
      },
    });
    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().auth;
      expect(state).toEqual({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        isBiometricEnabled: false,
        isBiometricAvailable: false,
        pendingRegistration: null,
        error: null,
        resetEmail: null,
      });
    });
  });

  describe('reducers', () => {
    it('should clear error', () => {
      // Set an error first
      store.dispatch({ type: 'auth/login/rejected', payload: 'Test error' });
      expect(store.getState().auth.error).toBe('Test error');

      // Clear the error
      store.dispatch({ type: 'auth/clearError' });
      expect(store.getState().auth.error).toBeNull();
    });

    it('should set pending registration', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phone: '1234567890',
        address: '123 Test St',
        profileImage: '',
        isEmailVerified: false,
        isPhoneVerified: false,
        userType: 'individual' as const,
        role: 'user' as const,
        idNumber: '123456789',
        occupation: 'Tester',
      };

      store.dispatch({ type: 'auth/setPendingRegistration', payload: mockUser });
      expect(store.getState().auth.pendingRegistration).toEqual(mockUser);
    });
  });

  describe('async thunks', () => {
    describe('initializeAuth', () => {
      it('should handle successful initialization', async () => {
        const mockUser = {
          id: '1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        };

        (AsyncStorage.getItem as jest.Mock)
          .mockResolvedValueOnce('mock-token')
          .mockResolvedValueOnce(JSON.stringify(mockUser));

        await store.dispatch(initializeAuth());

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.user).toEqual(mockUser);
        expect(state.isAuthenticated).toBe(true);
      });

      it('should handle initialization failure', async () => {
        (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));

        await store.dispatch(initializeAuth());

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.user).toBeNull();
        expect(state.isAuthenticated).toBe(false);
      });
    });

    describe('login', () => {
      it('should handle successful login', async () => {
        const mockResponse = {
          access_token: 'mock-token',
          token_type: 'bearer',
        };

        const mockUser = {
          id: '1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        };

        const { apiService } = require('../../../services/api');
        apiService.auth.login.mockResolvedValue(mockResponse);
        apiService.auth.getCurrentUser.mockResolvedValue(mockUser);

        await store.dispatch(login({ email: '<EMAIL>', password: 'password' }));

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.user).toEqual(mockUser);
        expect(state.isAuthenticated).toBe(true);
        expect(state.error).toBeNull();
      });

      it('should handle login failure', async () => {
        const { apiService } = require('../../../services/api');
        apiService.auth.login.mockRejectedValue(new Error('Invalid credentials'));

        await store.dispatch(login({ email: '<EMAIL>', password: 'wrong' }));

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.user).toBeNull();
        expect(state.isAuthenticated).toBe(false);
        expect(state.error).toBe('Invalid credentials');
      });
    });

    describe('forgotPassword', () => {
      it('should handle successful forgot password request', async () => {
        const { apiService } = require('../../../services/api');
        apiService.auth.forgotPassword.mockResolvedValue(undefined);

        await store.dispatch(forgotPassword('<EMAIL>'));

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.resetEmail).toBe('<EMAIL>');
        expect(state.error).toBeNull();
      });

      it('should handle forgot password failure', async () => {
        const { apiService } = require('../../../services/api');
        apiService.auth.forgotPassword.mockRejectedValue(new Error('Email not found'));

        await store.dispatch(forgotPassword('<EMAIL>'));

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.error).toBe('Email not found');
      });
    });

    describe('resetPassword', () => {
      it('should handle successful password reset', async () => {
        const { apiService } = require('../../../services/api');
        apiService.auth.resetPassword.mockResolvedValue(undefined);

        await store.dispatch(resetPassword({
          email: '<EMAIL>',
          code: 123456,
          new_password: 'newpassword',
        }));

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.resetEmail).toBeNull();
        expect(state.error).toBeNull();
      });

      it('should handle password reset failure', async () => {
        const { apiService } = require('../../../services/api');
        apiService.auth.resetPassword.mockRejectedValue(new Error('Invalid code'));

        await store.dispatch(resetPassword({
          email: '<EMAIL>',
          code: 999999,
          new_password: 'newpassword',
        }));

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.error).toBe('Invalid code');
      });
    });

    describe('logout', () => {
      it('should clear user data on logout', async () => {
        // First set some user data
        store.dispatch({
          type: 'auth/login/fulfilled',
          payload: {
            id: '1',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
          },
        });

        expect(store.getState().auth.isAuthenticated).toBe(true);

        // Then logout
        await store.dispatch(logout());

        const state = store.getState().auth;
        expect(state.user).toBeNull();
        expect(state.isAuthenticated).toBe(false);
        expect(state.error).toBeNull();
        expect(AsyncStorage.removeItem).toHaveBeenCalledWith('token');
        expect(AsyncStorage.removeItem).toHaveBeenCalledWith('user');
      });
    });
  });
});
