import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react-native';
import { createTheme } from '@/constants/theme';
import Toast from 'react-native-toast-message';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

/**
 * Enhanced Error Boundary component specifically designed for upload operations
 * Prevents app crashes and provides user-friendly error recovery options
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[ErrorBoundary] Caught error:', error);
    console.error('[ErrorBoundary] Error info:', errorInfo);

    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      try {
        this.props.onError(error, errorInfo);
      } catch (handlerError) {
        console.error('[ErrorBoundary] Error in custom error handler:', handlerError);
      }
    }

    // Log error for debugging
    this.logError(error, errorInfo);

    // Show toast notification
    Toast.show({
      type: 'error',
      text1: 'Something went wrong',
      text2: 'The app encountered an error but has recovered',
      visibilityTime: 5000,
    });
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    // Reset error boundary when resetKeys change
    if (hasError && resetOnPropsChange && resetKeys) {
      const prevResetKeys = prevProps.resetKeys || [];
      const hasResetKeyChanged = resetKeys.some(
        (resetKey, idx) => prevResetKeys[idx] !== resetKey
      );

      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  private logError = (error: Error, errorInfo: ErrorInfo) => {
    const errorReport = {
      timestamp: new Date().toISOString(),
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      userAgent: navigator.userAgent,
      url: window.location?.href || 'N/A',
    };

    // In a real app, you would send this to your error reporting service
    console.error('[ErrorBoundary] Error Report:', JSON.stringify(errorReport, null, 2));
  };

  private resetErrorBoundary = () => {
    console.log('[ErrorBoundary] Resetting error boundary');
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  private handleRetry = () => {
    console.log('[ErrorBoundary] User initiated retry');
    this.resetErrorBoundary();
  };

  private handleAutoRetry = () => {
    console.log('[ErrorBoundary] Auto retry in 3 seconds');
    
    Toast.show({
      type: 'info',
      text1: 'Auto Recovery',
      text2: 'Attempting to recover automatically...',
      visibilityTime: 3000,
    });

    this.resetTimeoutId = setTimeout(() => {
      this.resetErrorBoundary();
    }, 3000) as unknown as number;
  };

  private getErrorMessage = (error: Error): string => {
    // Provide user-friendly error messages for common upload errors
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'Network connection issue. Please check your internet connection and try again.';
    }
    
    if (message.includes('permission')) {
      return 'Permission denied. Please check app permissions and try again.';
    }
    
    if (message.includes('file') || message.includes('upload')) {
      return 'File upload failed. Please try selecting a different file.';
    }
    
    if (message.includes('camera')) {
      return 'Camera access failed. Please check camera permissions and try again.';
    }
    
    if (message.includes('memory') || message.includes('out of')) {
      return 'Insufficient memory. Please close other apps and try again.';
    }
    
    return 'An unexpected error occurred. Please try again.';
  };

  render() {
    const { hasError, error } = this.state;
    const { children, fallback } = this.props;

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Default error UI
      const theme = createTheme(false); // Use light theme for error UI
      const { colors, spacing, typography } = theme;

      const styles = StyleSheet.create({
        container: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: spacing.lg,
          backgroundColor: colors.background,
        },
        errorIcon: {
          marginBottom: spacing.lg,
        },
        title: {
          fontSize: typography.sizes.xl,
          fontWeight: typography.weights.bold,
          color: colors.error[500],
          textAlign: 'center',
          marginBottom: spacing.md,
        },
        message: {
          fontSize: typography.sizes.md,
          color: colors.textSecondary,
          textAlign: 'center',
          lineHeight: 24,
          marginBottom: spacing.xl,
          paddingHorizontal: spacing.md,
        },
        buttonContainer: {
          flexDirection: 'row',
          gap: spacing.md,
        },
        button: {
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: spacing.md,
          paddingHorizontal: spacing.lg,
          borderRadius: 8,
          minWidth: 120,
        },
        primaryButton: {
          backgroundColor: colors.primary[500],
        },
        secondaryButton: {
          backgroundColor: colors.gray[200],
          borderWidth: 1,
          borderColor: colors.gray[300],
        },
        buttonText: {
          fontSize: typography.sizes.md,
          fontWeight: typography.weights.medium,
          marginLeft: spacing.sm,
        },
        primaryButtonText: {
          color: colors.white,
        },
        secondaryButtonText: {
          color: colors.gray[700],
        },
        errorDetails: {
          marginTop: spacing.xl,
          padding: spacing.md,
          backgroundColor: colors.gray[100],
          borderRadius: 8,
          maxHeight: 200,
        },
        errorDetailsText: {
          fontSize: typography.sizes.sm,
          color: colors.gray[600],
          fontFamily: 'monospace',
        },
      });

      return (
        <View style={styles.container}>
          <AlertTriangle 
            size={64} 
            color={colors.error[500]} 
            style={styles.errorIcon}
          />
          
          <Text style={styles.title}>Oops! Something went wrong</Text>
          
          <Text style={styles.message}>
            {error ? this.getErrorMessage(error) : 'An unexpected error occurred'}
          </Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={this.handleRetry}
              activeOpacity={0.8}
            >
              <RefreshCw size={20} color={colors.white} />
              <Text style={[styles.buttonText, styles.primaryButtonText]}>
                Try Again
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={this.handleAutoRetry}
              activeOpacity={0.8}
            >
              <Home size={20} color={colors.gray[700]} />
              <Text style={[styles.buttonText, styles.secondaryButtonText]}>
                Auto Retry
              </Text>
            </TouchableOpacity>
          </View>

          {__DEV__ && error && (
            <ScrollView style={styles.errorDetails}>
              <Text style={styles.errorDetailsText}>
                {error.stack || error.message}
              </Text>
            </ScrollView>
          )}
        </View>
      );
    }

    return children;
  }
}

export default ErrorBoundary;
