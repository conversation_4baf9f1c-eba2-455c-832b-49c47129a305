// Backend API Types based on OpenAPI schema

// Enums
export enum PolicyType {
  MOTOR = 'motor',
  HOUSE_OWNER = 'house_owner',
  HOUSEHOLD_CONTENTS = 'household_contents',
  ALL_RISKS_SPECIFIED = 'all_risks_specified',
  ALL_RISKS_UNSPECIFIED = 'all_risks_unspecified',
  LIFE_ASSURANCE = 'life_assurance'
}

export enum PolicyProvider {
  HOLLAND = 'holland',
  BOTSWANA_LIFE = 'botswana_life'
}

export enum PolicyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export enum Status {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export enum ProfileStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export enum ConstructionType {
  BRICK = 'brick',
  THATCHED = 'thatched'
}

export enum SecurityType {
  BURGLAR_ALARM = 'burglar_alarm',
  CCTV = 'cctv',
  SECURITY_GUARD = 'security_guard'
}

export enum Use {
  PRIVATE = 'private',
  COMMERCIAL = 'commercial'
}

export enum IDType {
  NATIONAL = 'national',
  PASSPORT = 'passport'
}

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  UNDERWRITER = 'underwriter',
  SUPPORT = 'support'
}

export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed'
}

export enum PaymentCurrency {
  BWP = 'BWP'
}

export enum DocumentType {
  COPY_OF_VERIFIABLE_ID = 'copy_of_verifiable_id',
  PROOF_OF_ADDRESS = 'proof_of_address',
  PROOF_OF_INCOME = 'proof_of_income',
  BANK_CONFIRMATION_LETTER = 'bank_confirmation_letter',
  DRIVER_LICENSE = 'driver_license',
  VEHICLE_REGISTRATION_BOOK = 'vehicle_registration_book',
  VEHICLE_VALUATION_REPORT = 'vehicle_valuation_report',
  PREVIOUS_INSURANCE_POLICY = 'previous_insurance_policy',
  TITLE_DEED = 'title_deed',
  PROPERTY_VALUATION_REPORT = 'property_valuation_report',
  INVENTORY_FORM = 'inventory_form'
}

export enum Make {
  TOYOTA = 'toyota',
  BMW = 'bmw',
  MERCEDES_BENZ = 'mercedes_benz',
  VOLKSWAGEN = 'volkswagen',
  NISSAN = 'nissan',
  OTHERS = 'others'
}

export enum MessageParticipant {
  USER = 'user',
  AGENT = 'agent'
}

export enum ModelNames {
  POLICIES = 'policies',
  CLAIMS = 'claims',
  USERS = 'users',
  DOCUMENTS = 'documents'
}

export enum ClaimDocument {
  CLAIM_FORM = 'claim_form',
  POLICE_REPORT = 'police_report',
  MEDICAL_REPORT = 'medical_report',
  REPAIR_ESTIMATE = 'repair_estimate',
  PHOTOS = 'photos'
}

// Base interfaces
export interface InventoryItem {
  item: string;
  value: number;
}

// Chat System Types
export interface ChatSessionCreate {
  title?: string;
  participant_type?: MessageParticipant;
}

export interface ChatSessionPublic {
  id: string;
  user_id: string;
  title: string;
  created_at: string;
  updated_at: string;
  messages: ChatMessagePublic[];
}

export interface ChatMessageCreate {
  content: string;
  participant: MessageParticipant;
}

export interface ChatMessagePublic {
  id: string;
  session_id: string;
  content: string;
  participant: MessageParticipant;
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

// Claims Types
export interface ClaimCreate {
  policy_id: string;
  claim_type: string;
  description: string;
  incident_date: string;
  claim_amount?: number;
}

export interface ClaimUpdate {
  claim_type?: string;
  description?: string;
  incident_date?: string;
  claim_amount?: number;
  status?: Status;
}

export interface ClaimPublic {
  id: string;
  user_id: string;
  policy_id: string;
  claim_type: string;
  description: string;
  incident_date: string;
  claim_amount?: number;
  status: Status;
  reviewer_id?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
  documents: DocumentPublic[];
}

// Payment Types
export interface PaymentCreate {
  policy_id?: string;
  currency?: PaymentCurrency;
  amount: number;
  payment_date: string;
}

export interface PaymentUpdate {
  policy_id?: string;
  currency?: PaymentCurrency;
  amount?: number;
  payment_date?: string;
  status?: PaymentStatus;
}

export interface PaymentPublic {
  id: string;
  user_id: string;
  policy_id?: string;
  currency: PaymentCurrency;
  amount: number;
  status: PaymentStatus;
  created_at: string;
  updated_at: string;
}

// Policy Details Types
export interface CarAdditionalExtensions {
  radio_cassette_player?: boolean;
  loss_of_use?: boolean;
  towing_costs?: boolean;
  personal_accident?: boolean;
}

export interface MotorDocuments {
  driver_license?: boolean;
  vehicle_registration_book?: boolean;
  vehicle_valuation_report?: boolean;
  previous_insurance_policy?: boolean;
}

export interface MotorPolicyDetails {
  make: Make;
  model: string;
  year: number;
  engine_number: string;
  chassis_number: string;
  registration_number: string;
  current_mileage: number;
  insured_value: number;
  use: Use;
  is_grey_import?: boolean;
  addtional_extensions: CarAdditionalExtensions;
  required_documents: MotorDocuments;
}

export interface HouseOwnerDocuments {
  title_deed?: boolean;
  property_valuation_report?: boolean;
}

export interface HouseOwnerPolicyDetails {
  property_value: number;
  year_of_construction: number;
  sum_insured: number;
  construction_type: ConstructionType;
  is_occupied: boolean;
  required_documents: HouseOwnerDocuments;
}

export interface HouseholdContentsDocuments {
  inventory_form?: boolean;
}

export interface HouseholdContentsPolicyDetails {
  type_of_security: SecurityType;
  inventory_list: InventoryItem[];
  power_surge_extension?: boolean;
  accidental_breakage_extension?: boolean;
  sum_insured: number;
  required_documents: HouseholdContentsDocuments;
}

export interface AllRisksSpecifiedItemsPolicyDetails {
  items: InventoryItem[];
  sum_insured: number;
}

export interface AllRisksUnspecifiedItemsPolicyDetails {
  total_sum_insured: number;
  items: InventoryItem[];
  sum_insured: number;
}

export interface BotswanaLifePolicyDetails {
  sum_insured: number;
  policy_term?: number;
  premium_payment_frequency?: string;
  beneficiaries: Array<{
    name: string;
    relationship: string;
    percentage: number;
  }>;
}

export type PolicyDetails =
  | MotorPolicyDetails
  | HouseOwnerPolicyDetails
  | HouseholdContentsPolicyDetails
  | AllRisksSpecifiedItemsPolicyDetails
  | AllRisksUnspecifiedItemsPolicyDetails
  | BotswanaLifePolicyDetails;

// User Types
export interface UserCreate {
  email: string;
  hashed_password: string;
  username?: string;
  phone_number?: string;
  role?: UserRole;
}

export interface UserUpdate {
  email?: string;
  hashed_password?: string;
  username?: string;
  phone_number?: string;
  role?: UserRole;
  is_phone_number_verified?: boolean;
  is_email_verified?: boolean;
}

export interface UserPublic {
  id: string;
  email: string;
  username?: string;
  phone_number?: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
  is_phone_number_verified: boolean;
  is_email_verified: boolean;
}

export interface UserPasswordReset {
  email: string;
  code: number;
  new_password: string;
}

// User Profile Types
export interface UserProfileCreate {
  other_names: string;
  surname_name: string;
  date_of_birth: string;
  postal_address: string;
  verifiable_id: string;
  id_type: IDType;
  occupation: string;
  status?: ProfileStatus;
  copy_of_verifiable_id?: boolean;
  proof_of_address?: boolean;
  proof_of_income?: boolean;
  bank_confirmation_letter?: boolean;
  kyc_acknowledgement?: boolean;
}

export interface UserProfileUpdate {
  other_names?: string;
  surname_name?: string;
  date_of_birth?: string;
  postal_address?: string;
  verifiable_id?: string;
  id_type?: IDType;
  occupation?: string;
  status?: ProfileStatus;
  copy_of_verifiable_id?: boolean;
  proof_of_address?: boolean;
  proof_of_income?: boolean;
  bank_confirmation_letter?: boolean;
  kyc_acknowledgement?: boolean;
}

export interface UserProfilePublic {
  id: string;
  user_id: string;
  other_names: string;
  surname_name: string;
  date_of_birth: string;
  postal_address: string;
  verifiable_id: string;
  id_type: IDType;
  occupation: string;
  status: ProfileStatus;
  copy_of_verifiable_id: boolean;
  proof_of_address: boolean;
  proof_of_income: boolean;
  bank_confirmation_letter: boolean;
  kyc_acknowledgement: boolean;
  reviewer_id?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

// Policy Types
export interface PolicyCreate {
  policy_type?: PolicyType;
  policy_provider?: PolicyProvider;
  status: PolicyStatus;
  start_date: string;
  end_date: string;
  policy_details: PolicyDetails;
  user_id: string;
}

export interface PolicyUpdate {
  policy_type?: PolicyType;
  policy_provider?: PolicyProvider;
  tracker_status?: TrackerStatus;
  status?: PolicyStatus;
  start_date?: string;
  end_date?: string;
  policy_details?: PolicyDetails;
}

export interface PolicyPublic {
  id: string;
  user_id: string;
  policy_type: PolicyType;
  policy_provider: PolicyProvider;
  status: PolicyStatus;
  start_date: string;
  end_date: string;
  policy_details: PolicyDetails;
  quotation?: number;
  tracker_status: TrackerStatus;
  documents: DocumentPublic[];
  reviewer_id?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

export enum TrackerStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  ACTIVE = 'active',
  EXPIRED = 'expired'
}

// Document Types
export interface DocumentPublic {
  id: string;
  user_id: string;
  policy_id?: string;
  claim_id?: string;
  name: string;
  document_type: DocumentType;
  file_path: string;
  status: Status;
  reviewer_id?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentUpdate {
  status: Status;
}

export interface DocumentUploadRequest {
  document_type: DocumentType;
  policy_id?: string;
  claim_id?: string;
}

// Auth Types
export interface Token {
  access_token: string;
  token_type: string;
  expires_in?: number;
  refresh_token?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  grant_type?: string;
  scope?: string;
  client_id?: string;
  client_secret?: string;
}

// Error Types
export interface ValidationError {
  loc: (string | number)[];
  msg: string;
  type: string;
}

export interface HTTPValidationError {
  detail: ValidationError[];
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Admin Analytics Types
export interface ModelCountResponse {
  count: number;
  period: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  count: number;
}

// File Upload Types
export interface FileUploadResponse {
  id: string;
  filename: string;
  file_path: string;
  content_type: string;
  size: number;
}
