import React, { 
  createContext, 
  useContext, 
  useRef, 
  useCallback, 
  useMemo,
  useState,
  useEffect
} from 'react';
import { Document, DocumentCategory } from '@/components/documents/types';
import { UploadManager, UploadResult, UploadProgress } from '@/utils/UploadManager';
import { MemoryManager } from '@/utils/MemoryManager';

// Optimized context type with minimal state
interface OptimizedUploadContextType {
  // Core upload methods
  uploadImage: (useCamera: boolean, documentType: string, documentCategory: DocumentCategory) => Promise<string>;
  uploadDocument: (documentType: string, documentCategory: DocumentCategory) => Promise<string>;
  
  // Progress tracking
  getUploadProgress: (uploadId: string) => UploadProgress | null;
  subscribeToProgress: (uploadId: string, callback: (progress: UploadProgress) => void) => () => void;
  
  // Upload management
  cancelUpload: (uploadId: string) => boolean;
  clearCompletedUploads: () => void;
  
  // Status queries (non-reactive)
  getQueueStatus: () => { queueLength: number; activeUploads: number; isProcessing: boolean };
  isInLowMemoryMode: () => boolean;
  
  // Document management (minimal reactive state)
  getVerifiedDocuments: () => Document[];
  addVerifiedDocument: (document: Document) => void;
  removeVerifiedDocument: (documentId: string) => void;
}

// Create context with stable default value
const OptimizedUploadContext = createContext<OptimizedUploadContextType>({
  uploadImage: async () => '',
  uploadDocument: async () => '',
  getUploadProgress: () => null,
  subscribeToProgress: () => () => {},
  cancelUpload: () => false,
  clearCompletedUploads: () => {},
  getQueueStatus: () => ({ queueLength: 0, activeUploads: 0, isProcessing: false }),
  isInLowMemoryMode: () => false,
  getVerifiedDocuments: () => [],
  addVerifiedDocument: () => {},
  removeVerifiedDocument: () => {},
});

// Custom hook with stable reference
export const useOptimizedUpload = () => {
  const context = useContext(OptimizedUploadContext);
  if (!context) {
    throw new Error('useOptimizedUpload must be used within OptimizedUploadProvider');
  }
  return context;
};

// Provider component with minimal rerenders
// Hook for components that need reactive document updates
export const useVerifiedDocuments = () => {
  const context = useContext(OptimizedUploadContext);
  if (!context) {
    throw new Error('useVerifiedDocuments must be used within OptimizedUploadProvider');
  }
  return {
    documents: context.getVerifiedDocuments(),
    addDocument: context.addVerifiedDocument,
    removeDocument: context.removeVerifiedDocument,
  };
};

// Hook for upload operations (non-reactive)
export const useUploadOperations = () => {
  const context = useContext(OptimizedUploadContext);
  if (!context) {
    throw new Error('useUploadOperations must be used within OptimizedUploadProvider');
  }
  return {
    uploadImage: context.uploadImage,
    uploadDocument: context.uploadDocument,
    cancelUpload: context.cancelUpload,
    clearCompleted: context.clearCompletedUploads,
    getQueueStatus: context.getQueueStatus,
    isLowMemoryMode: context.isInLowMemoryMode,
  };
};

// Hook for progress tracking (non-reactive)
export const useUploadProgress = () => {
  const context = useContext(OptimizedUploadContext);
  if (!context) {
    throw new Error('useUploadProgress must be used within OptimizedUploadProvider');
  }
  return {
    getProgress: context.getUploadProgress,
    subscribe: context.subscribeToProgress,
  };
};

export const OptimizedUploadProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  // Only track verified documents in React state (minimal reactive state)
  const [verifiedDocuments, setVerifiedDocuments] = useState<Document[]>([]);
  
  // Use refs for non-reactive data
  const progressCallbacksRef = useRef<Map<string, (progress: UploadProgress) => void>>(new Map());
  const uploadPromisesRef = useRef<Map<string, { resolve: (value: string) => void; reject: (error: Error) => void }>>(new Map());
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all progress callbacks
      progressCallbacksRef.current.clear();
      
      // Reject any pending upload promises
      for (const { reject } of uploadPromisesRef.current.values()) {
        reject(new Error('Upload context unmounted'));
      }
      uploadPromisesRef.current.clear();
    };
  }, []);

  // Memoized upload methods
  const uploadImage = useCallback(async (
    useCamera: boolean,
    documentType: string,
    documentCategory: DocumentCategory
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      try {
        const uploadId = UploadManager.pickImage(
          useCamera,
          documentType,
          documentCategory,
          (result: UploadResult) => {
            // Clean up promise tracking
            uploadPromisesRef.current.delete(uploadId);
            
            if (result.success && result.document) {
              // Add to verified documents after successful upload
              setVerifiedDocuments(prev => {
                // Check if document already exists
                const exists = prev.some(doc => doc.id === result.document!.id);
                if (exists) return prev;
                
                return [result.document!, ...prev];
              });
              
              resolve(uploadId);
            } else {
              reject(new Error(result.error || 'Upload failed'));
            }
          }
        );
        
        // Track the promise for cleanup
        uploadPromisesRef.current.set(uploadId, { resolve, reject });
        
        // Track resource for memory management
        MemoryManager.trackResource(
          uploadId,
          'upload',
          () => {
            uploadPromisesRef.current.delete(uploadId);
            progressCallbacksRef.current.delete(uploadId);
          }
        );
        
      } catch (error) {
        reject(error instanceof Error ? error : new Error('Upload initialization failed'));
      }
    });
  }, []);

  const uploadDocument = useCallback(async (
    documentType: string,
    documentCategory: DocumentCategory
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      try {
        const uploadId = UploadManager.pickDocument(
          documentType,
          documentCategory,
          (result: UploadResult) => {
            // Clean up promise tracking
            uploadPromisesRef.current.delete(uploadId);
            
            if (result.success && result.document) {
              // Add to verified documents after successful upload
              setVerifiedDocuments(prev => {
                // Check if document already exists
                const exists = prev.some(doc => doc.id === result.document!.id);
                if (exists) return prev;
                
                return [result.document!, ...prev];
              });
              
              resolve(uploadId);
            } else {
              reject(new Error(result.error || 'Upload failed'));
            }
          }
        );
        
        // Track the promise for cleanup
        uploadPromisesRef.current.set(uploadId, { resolve, reject });
        
        // Track resource for memory management
        MemoryManager.trackResource(
          uploadId,
          'upload',
          () => {
            uploadPromisesRef.current.delete(uploadId);
            progressCallbacksRef.current.delete(uploadId);
          }
        );
        
      } catch (error) {
        reject(error instanceof Error ? error : new Error('Upload initialization failed'));
      }
    });
  }, []);

  // Progress tracking methods
  const getUploadProgress = useCallback((uploadId: string): UploadProgress | null => {
    return UploadManager.getUploadProgress(uploadId);
  }, []);

  const subscribeToProgress = useCallback((
    uploadId: string,
    callback: (progress: UploadProgress) => void
  ): (() => void) => {
    // Store callback
    progressCallbacksRef.current.set(uploadId, callback);
    
    // Register with upload manager
    UploadManager.registerProgressCallback(uploadId, callback);
    
    // Return unsubscribe function
    return () => {
      progressCallbacksRef.current.delete(uploadId);
      // Note: UploadManager handles its own callback cleanup
    };
  }, []);

  // Upload management methods
  const cancelUpload = useCallback((uploadId: string): boolean => {
    const success = UploadManager.cancelUpload(uploadId);
    
    if (success) {
      // Clean up local tracking
      const promise = uploadPromisesRef.current.get(uploadId);
      if (promise) {
        promise.reject(new Error('Upload cancelled'));
        uploadPromisesRef.current.delete(uploadId);
      }
      
      progressCallbacksRef.current.delete(uploadId);
      MemoryManager.untrackResource(uploadId);
    }
    
    return success;
  }, []);

  const clearCompletedUploads = useCallback(() => {
    UploadManager.clearCompletedUploads();
  }, []);

  // Status query methods (non-reactive)
  const getQueueStatus = useCallback(() => {
    return UploadManager.queueStatus;
  }, []);

  const isInLowMemoryMode = useCallback(() => {
    return MemoryManager.isInLowMemoryMode();
  }, []);

  // Document management methods
  const getVerifiedDocuments = useCallback(() => {
    return verifiedDocuments;
  }, [verifiedDocuments]);

  const addVerifiedDocument = useCallback((document: Document) => {
    setVerifiedDocuments(prev => {
      // Check if document already exists
      const exists = prev.some(doc => doc.id === document.id);
      if (exists) return prev;
      
      return [document, ...prev];
    });
  }, []);

  const removeVerifiedDocument = useCallback((documentId: string) => {
    setVerifiedDocuments(prev => prev.filter(doc => doc.id !== documentId));
  }, []);

  // Memoize context value to prevent unnecessary rerenders
  const contextValue = useMemo<OptimizedUploadContextType>(() => ({
    uploadImage,
    uploadDocument,
    getUploadProgress,
    subscribeToProgress,
    cancelUpload,
    clearCompletedUploads,
    getQueueStatus,
    isInLowMemoryMode,
    getVerifiedDocuments,
    addVerifiedDocument,
    removeVerifiedDocument,
  }), [
    uploadImage,
    uploadDocument,
    getUploadProgress,
    subscribeToProgress,
    cancelUpload,
    clearCompletedUploads,
    getQueueStatus,
    isInLowMemoryMode,
    getVerifiedDocuments,
    addVerifiedDocument,
    removeVerifiedDocument,
  ]);

  return (
    <OptimizedUploadContext.Provider value={contextValue}>
      {children}
    </OptimizedUploadContext.Provider>
  );
};
