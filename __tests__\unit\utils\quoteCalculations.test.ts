import {
  calculateMotorPremium,
  calculateHouseownersPremium,
  calculateHouseholdContentsPremium,
  calculateAllRisksPremium,
  formatCurrency,
  validateVehicleDetails,
  validatePropertyDetails,
} from '../../../utils/quoteCalculations';

describe('quoteCalculations', () => {
  describe('calculateMotorPremium', () => {
    it('should calculate Toyota premium correctly', () => {
      const vehicleValue = 100000;
      const brand = 'Toyota';
      const isGreyImport = false;

      const result = calculateMotorPremium(vehicleValue, brand, isGreyImport);

      expect(result.premium).toBe(3000); // 3% of 100,000
      expect(result.rate).toBe(3);
      expect(result.minimumPremium).toBe(3000);
    });

    it('should apply minimum premium when calculated premium is below minimum', () => {
      const vehicleValue = 50000; // Would calculate to 1,500 (3% of 50,000)
      const brand = 'Toyota';
      const isGreyImport = false;

      const result = calculateMotorPremium(vehicleValue, brand, isGreyImport);

      expect(result.premium).toBe(3000); // Minimum premium applied
      expect(result.rate).toBe(3);
      expect(result.minimumPremium).toBe(3000);
    });

    it('should calculate Mercedes-Benz premium correctly', () => {
      const vehicleValue = 200000;
      const brand = 'Mercedes-Benz';
      const isGreyImport = false;

      const result = calculateMotorPremium(vehicleValue, brand, isGreyImport);

      expect(result.premium).toBe(6700); // 3.35% of 200,000
      expect(result.rate).toBe(3.35);
    });

    it('should calculate grey import premium correctly', () => {
      const vehicleValue = 100000;
      const brand = 'Toyota';
      const isGreyImport = true;

      const result = calculateMotorPremium(vehicleValue, brand, isGreyImport);

      expect(result.premium).toBe(5000); // 5% of 100,000 for grey import Toyota
      expect(result.rate).toBe(5);
    });

    it('should calculate other brand grey import premium correctly', () => {
      const vehicleValue = 100000;
      const brand = 'Honda';
      const isGreyImport = true;

      const result = calculateMotorPremium(vehicleValue, brand, isGreyImport);

      expect(result.premium).toBe(6500); // 6.5% of 100,000 for other grey import
      expect(result.rate).toBe(6.5);
    });

    it('should handle unknown brand as "All Other"', () => {
      const vehicleValue = 100000;
      const brand = 'Unknown Brand';
      const isGreyImport = false;

      const result = calculateMotorPremium(vehicleValue, brand, isGreyImport);

      expect(result.premium).toBe(3500); // 3.5% for "All Other"
      expect(result.rate).toBe(3.5);
    });
  });

  describe('calculateHouseownersPremium', () => {
    it('should calculate premium for property up to 1,000,000', () => {
      const propertyValue = 800000;
      const constructionType = 'standard';

      const result = calculateHouseownersPremium(propertyValue, constructionType);

      expect(result.premium).toBe(600); // 0.075% of 800,000
      expect(result.rate).toBe(0.075);
    });

    it('should calculate premium for property up to 2,500,000', () => {
      const propertyValue = 2000000;
      const constructionType = 'standard';

      const result = calculateHouseownersPremium(propertyValue, constructionType);

      expect(result.premium).toBe(1400); // 0.070% of 2,000,000
      expect(result.rate).toBe(0.070);
    });

    it('should calculate premium for thatched property', () => {
      const propertyValue = 1000000;
      const constructionType = 'thatched';

      const result = calculateHouseownersPremium(propertyValue, constructionType);

      expect(result.premium).toBe(6500); // 0.65% of 1,000,000
      expect(result.rate).toBe(0.65);
    });
  });

  describe('calculateHouseholdContentsPremium', () => {
    it('should calculate premium for 100,000 contents', () => {
      const contentsValue = 100000;

      const result = calculateHouseholdContentsPremium(contentsValue);

      expect(result.premium).toBe(750); // 0.75% of 100,000
      expect(result.rate).toBe(0.75);
    });

    it('should calculate premium for 250,000 contents', () => {
      const contentsValue = 250000;

      const result = calculateHouseholdContentsPremium(contentsValue);

      expect(result.premium).toBe(1750); // 0.70% of 250,000
      expect(result.rate).toBe(0.70);
    });

    it('should calculate premium for 500,000 contents', () => {
      const contentsValue = 500000;

      const result = calculateHouseholdContentsPremium(contentsValue);

      expect(result.premium).toBe(3250); // 0.65% of 500,000
      expect(result.rate).toBe(0.65);
    });
  });

  describe('calculateAllRisksPremium', () => {
    it('should calculate all risks premium', () => {
      const itemValue = 50000;

      const result = calculateAllRisksPremium(itemValue);

      expect(result.premium).toBe(2000); // 4% of 50,000
      expect(result.rate).toBe(4);
    });
  });

  describe('formatCurrency', () => {
    it('should format currency with P symbol', () => {
      expect(formatCurrency(1000)).toBe('P 1,000.00');
      expect(formatCurrency(1234.56)).toBe('P 1,234.56');
      expect(formatCurrency(0)).toBe('P 0.00');
    });

    it('should handle large numbers', () => {
      expect(formatCurrency(1000000)).toBe('P 1,000,000.00');
      expect(formatCurrency(1234567.89)).toBe('P 1,234,567.89');
    });
  });

  describe('validateVehicleDetails', () => {
    it('should validate complete vehicle details', () => {
      const vehicleDetails = {
        make: 'Toyota',
        model: 'Camry',
        year: 2020,
        engineNumber: 'ENG123456',
        chassisNumber: 'CHAS123456',
        registrationNumber: 'B123ABC',
        currentMileage: 50000,
        use: 'private' as const,
        value: 200000,
      };

      const result = validateVehicleDetails(vehicleDetails);

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should return errors for missing required fields', () => {
      const vehicleDetails = {
        make: '',
        model: 'Camry',
        year: 2020,
        engineNumber: '',
        chassisNumber: 'CHAS123456',
        registrationNumber: 'B123ABC',
        currentMileage: 50000,
        use: 'private' as const,
        value: 0,
      };

      const result = validateVehicleDetails(vehicleDetails);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Make is required');
      expect(result.errors).toContain('Engine number is required');
      expect(result.errors).toContain('Vehicle value must be greater than 0');
    });

    it('should validate year range', () => {
      const vehicleDetails = {
        make: 'Toyota',
        model: 'Camry',
        year: 1980, // Too old
        engineNumber: 'ENG123456',
        chassisNumber: 'CHAS123456',
        registrationNumber: 'B123ABC',
        currentMileage: 50000,
        use: 'private' as const,
        value: 200000,
      };

      const result = validateVehicleDetails(vehicleDetails);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Year must be between 1990 and current year');
    });
  });

  describe('validatePropertyDetails', () => {
    it('should validate complete property details', () => {
      const propertyDetails = {
        address: '123 Main Street, Gaborone',
        constructionType: 'standard' as const,
        occupancyStatus: 'owner-occupied' as const,
        value: 1000000,
        yearBuilt: 2010,
      };

      const result = validatePropertyDetails(propertyDetails);

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should return errors for missing required fields', () => {
      const propertyDetails = {
        address: '',
        constructionType: 'standard' as const,
        occupancyStatus: 'owner-occupied' as const,
        value: 0,
        yearBuilt: 1800, // Too old
      };

      const result = validatePropertyDetails(propertyDetails);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Address is required');
      expect(result.errors).toContain('Property value must be greater than 0');
      expect(result.errors).toContain('Year built must be after 1900');
    });
  });
});
