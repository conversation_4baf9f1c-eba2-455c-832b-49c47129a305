# Inerca Holdings Mobile Application

A comprehensive insurance mobile application built with React Native and Expo, designed to streamline insurance operations for Inerca Holdings in Botswana.

## 🚀 **Current Status**

- ✅ **Production Ready**: Core features implemented and tested
- ✅ **Performance Optimized**: Advanced upload system with memory management
- ✅ **Security Enhanced**: Fixed Google OAuth, comprehensive authentication
- ✅ **Testing Suite**: Complete testing infrastructure with 70%+ coverage
- ✅ **Documentation**: Comprehensive guides and API documentation

## ✨ **Key Features**

### **Authentication & Security** ✅
- **Secure Authentication**: Login/registration with JWT tokens
- **Google OAuth**: Fixed and optimized OAuth integration
- **Biometric Support**: Fingerprint and face recognition
- **Password Reset**: Complete forgot password flow with email verification
- **Multi-factor Authentication**: OTP verification support

### **Insurance Services** ✅
- **Quote Generation**: Motor, Houseowners, Household Contents, All Risks, Life Assurance
- **Premium Calculations**: Accurate rates based on Botswana market (Toyota 3%, Mercedes 3.35%, etc.)
- **Policy Management**: Complete policy lifecycle management
- **Claims Processing**: Submit and track insurance claims
- **Document Verification**: Advanced document upload with verification status

### **Advanced Features** ✅
- **Application Tracking**: Real-time status monitoring with progress visualization
- **Notification System**: In-app and push notifications for important updates
- **Document Management**: Secure upload system with memory optimization
- **Policy Agreement**: Legal declaration component with scroll-to-read requirement
- **Multi-language Ready**: English with future Setswana support
- **Theme Support**: Dark/Light mode with system detection

## 🛠 **Tech Stack**

### **Core Technologies**
- **Framework**: React Native 0.74+ with Expo SDK 51+
- **State Management**: Redux Toolkit with optimized slices
- **Navigation**: Expo Router with type-safe routing
- **UI Framework**: Custom components with Expo Vector Icons
- **Storage**: AsyncStorage with secure token management

### **Performance & Optimization**
- **Upload System**: Queue-based system with memory management
- **Error Boundaries**: Multiple layers of error protection
- **Memory Management**: Automated cleanup and resource tracking
- **Component Optimization**: React.memo and memoization patterns

### **Testing & Quality**
- **Testing Framework**: Jest with React Native Testing Library
- **Coverage**: 70%+ code coverage with detailed reports
- **E2E Testing**: Complete user flow testing
- **Performance Testing**: Memory and render performance monitoring

## 🏗 **Project Structure**

```
inerca-app/
├── app/                    # Expo Router screens
│   ├── (auth)/            # Authentication screens
│   ├── (app)/             # Main app screens
│   └── (onboarding)/      # Onboarding flow
├── components/            # Reusable components
│   ├── common/           # Shared components
│   ├── forms/            # Form components
│   ├── optimized/        # Performance-optimized components
│   └── ui/               # UI components
├── store/                # Redux store and slices
├── services/             # API services and utilities
├── utils/                # Utility functions
├── constants/            # App constants and themes
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── __tests__/            # Test files
    ├── unit/             # Unit tests
    ├── integration/      # Integration tests
    ├── component/        # Component tests
    └── e2e/              # End-to-end tests
```

## 🚀 **Getting Started**

### **Prerequisites**
- Node.js 18+ and npm/yarn
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (Mac) or Android Studio
- Git for version control

### **Installation**
```bash
# Clone the repository
git clone <repository-url>
cd inerca-app

# Install dependencies
npm install

# Start the development server
npm run dev

# Run on specific platform
npm run ios     # iOS Simulator
npm run android # Android Emulator
npm run web     # Web browser
```

### **Environment Setup**
Create a `.env` file in the root directory:
```env
EXPO_PUBLIC_API_BASE_URL=https://inerca-backend-wild-leaf-8326.fly.dev/
EXPO_PUBLIC_ENVIRONMENT=development
```

## 🧪 **Testing**

### **Available Test Commands**
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test types
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:component     # Component tests only
npm run test:e2e          # End-to-end tests only

# Development testing
npm run test:watch        # Watch mode
npm run test:ci          # CI mode with coverage
```

### **Coverage Requirements**
- **Global**: 70% minimum coverage
- **Authentication**: 100% coverage
- **Payment Processing**: 100% coverage
- **Quote Calculations**: 90% coverage

## 📱 **Features Overview**

### **Authentication Flow**
1. **Registration**: Email/phone with OTP verification
2. **Login**: Email/password with biometric option
3. **Google OAuth**: Seamless Google sign-in integration
4. **Password Reset**: Email-based password recovery
5. **Profile Management**: Complete user profile system

### **Insurance Operations**
1. **Quote Generation**: Multi-step forms for different insurance types
2. **Document Upload**: Secure document management with verification
3. **Application Tracking**: Real-time status monitoring
4. **Policy Management**: View and manage active policies
5. **Claims Processing**: Submit and track insurance claims

### **User Experience**
1. **Dark/Light Theme**: System-aware theme switching
2. **Notifications**: In-app and push notification system
3. **Offline Support**: Basic offline functionality
4. **Multi-language**: English with Setswana support planned
5. **Accessibility**: Screen reader and keyboard navigation support

## 🔧 **Development**

### **Code Quality**
```bash
# Linting
npm run lint

# Type checking
npx tsc --noEmit

# Format code
npx prettier --write .
```

### **Performance Monitoring**
- Memory usage tracking
- Component render monitoring
- Upload performance metrics
- Error boundary reporting

### **Debugging**
- React Native Debugger
- Flipper integration
- Console logging system
- Error tracking

## 📚 **Documentation**

### **Available Guides**
- **[Testing Guide](TESTING.md)**: Comprehensive testing documentation
- **[Performance Report](PERFORMANCE_OPTIMIZATION_REPORT.md)**: Performance analysis and optimizations
- **[Policy Agreement Component](POLICY_AGREEMENT_COMPONENT.md)**: Legal component documentation
- **[Google OAuth Fix](GOOGLE_OAUTH_FIX.md)**: OAuth implementation details
- **[Upload System Migration](UPLOAD_SYSTEM_MIGRATION.md)**: Upload system documentation

### **API Documentation**
- Backend API: `https://inerca-backend-wild-leaf-8326.fly.dev/docs/`
- OpenAPI Spec: `https://inerca-backend-wild-leaf-8326.fly.dev/openapi.json`
- Backend Schema: `backend.json`

## 🚀 **Deployment**

### **Build Commands**
```bash
# Development build
npx expo build:android --type apk
npx expo build:ios --type archive

# Production build
npx expo build:android --type app-bundle --release-channel production
npx expo build:ios --type archive --release-channel production

# Web build
npm run build:web
```

### **Environment Configuration**
- **Development**: Local development with mock data
- **Staging**: Testing environment with backend integration
- **Production**: Live environment with full backend integration

## 🐛 **Known Issues**

### **Resolved Issues** ✅
- ✅ Google OAuth black screen issue (Fixed)
- ✅ Upload system memory leaks (Fixed)
- ✅ Component rerender performance (Optimized)
- ✅ Authentication token handling (Improved)

### **Pending Issues** ⚠️
- Admin dashboard implementation needed
- Live chat system not implemented
- Advanced search functionality pending
- Localization (Setswana) not implemented

## 🤝 **Contributing**

### **Development Workflow**
1. Create feature branch from `main`
2. Implement changes with tests
3. Run test suite and ensure coverage
4. Submit pull request with description
5. Code review and merge

### **Code Standards**
- TypeScript for type safety
- ESLint and Prettier for code formatting
- Jest for testing with minimum 70% coverage
- React Native best practices
- Performance optimization patterns

## 📄 **License**

This project is proprietary software developed for Inerca Holdings. All rights reserved.

## 📞 **Support**

For technical support or questions:
- **Developer**: Quiver Tech
- **Lead Developer**: Etornam Alfred Klu
- **Documentation**: Comprehensive guides in `/docs` folder
- **API Support**: Backend documentation at `/docs/` endpoint

---

**Version**: 1.0.0  
**Last Updated**: January 2025  
**Status**: Production Ready ✅
