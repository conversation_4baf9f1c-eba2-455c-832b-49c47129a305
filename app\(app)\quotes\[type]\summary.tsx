import React, { useState, useEffect } from 'react';
import { View, Alert, Text, StyleSheet } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import QuoteFormContainer from '@/components/quotes/QuoteFormContainer';
import PremiumCalculationBreakdown from '@/components/quotes/PremiumCalculationBreakdown';
import RequiredDocumentsList from '@/components/quotes/RequiredDocumentsList';
import OptionalExtrasSelector from '@/components/quotes/OptionalExtrasSelector';
import useQuoteStore from '@/store/quoteStore';
import { QuoteDocument, InsuranceExtra } from '@/types/quote.types';
import { showToast } from '@/utils/toast';
import { useVerifiedDocuments } from '@/context/OptimizedUploadContext';
import useApplicationStore from '@/store/applicationStore';
import { formatCurrency } from '@/utils/quoteCalculations';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { createPolicy } from '@/store/policySlice';
import { convertQuoteToPolicy, validateQuoteForPolicyCreation } from '@/utils/policyConversion';

export default function QuoteSummaryScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const { type: quoteType, quoteId } = useLocalSearchParams();
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.auth.user);
  const { documents: verifiedDocuments } = useVerifiedDocuments();

  // Mock compatibility functions
  const areAllDocumentsVerified = (documentIds: string[]) => {
    return documentIds.every(id => verifiedDocuments.some(doc => doc.id === id));
  };
  const documentsInVerification: string[] = [];
  const getVerifiedDocumentByType = (documentType: string) => {
    return verifiedDocuments.find(doc => doc.name === documentType) || null;
  };

  // Get quote store functions
  const {
    getQuoteById,
    updateQuote,
    updateQuoteStatus,
    setCurrentQuote,
    currentQuote,
    isLoading
  } = useQuoteStore();

  // Initialize with empty arrays - will be populated based on quote data
  const [premiumItems, setPremiumItems] = useState<any[]>([]);
  const [basePremium, setBasePremium] = useState(0);
  const [totalPremium, setTotalPremium] = useState(0);
  const [requiredDocuments, setRequiredDocuments] = useState<QuoteDocument[]>([]);
  const [optionalExtras, setOptionalExtras] = useState<InsuranceExtra[]>([]);

  // Load quote data
  useEffect(() => {
    if (quoteId) {
      const quote = getQuoteById(quoteId as string);
      if (quote) {
        setCurrentQuote(quote);

        // Use premium from quote or calculate from API
        const base = quote.premium || 0;
        setBasePremium(base);
        setTotalPremium(base);

        // Premium breakdown will be fetched from API when available
        // For now, use the quote premium as-is
        setPremiumItems([]);

        // Use the documents from the quote if available, otherwise set up defaults
        if (quote.documents && quote.documents.length > 0) {
          setRequiredDocuments(quote.documents);
        } else {
          // Set up default required documents
          const docs: QuoteDocument[] = [
            {
              id: '1',
              name: 'National ID/Omang/Passport',
              type: 'National ID/Omang/Passport',
              category: 'ID',
              required: true,
              uploaded: false,
            },
            {
              id: '2',
              name: 'Proof of Address',
              type: 'Proof of Address',
              category: 'Proof of Address',
              required: true,
              uploaded: false,
            },
          ];

          // Add type-specific documents
          if (quote.type === 'motor') {
            docs.push({
              id: '3',
              name: 'Driver\'s License',
              type: 'Driver\'s License',
              category: 'ID',
              required: true,
              uploaded: false,
            });
            docs.push({
              id: '4',
              name: 'Vehicle Registration Book',
              type: 'Vehicle Registration Book',
              category: 'ID',
              required: true,
              uploaded: false,
            });
          } else if (quote.type === 'houseowners') {
            docs.push({
              id: '3',
              name: 'Title Deed/Land Board Certificate',
              type: 'Title Deed/Land Board Certificate',
              category: 'ID',
              required: true,
              uploaded: false,
            });
            docs.push({
              id: '4',
              name: 'Building Valuation Report',
              type: 'Building Valuation Report',
              category: 'Insurance',
              required: true,
              uploaded: false,
            });
          } else if (quote.type === 'householdContents') {
            docs.push({
              id: '3',
              name: 'Completed Inventory Form',
              type: 'Completed Inventory Form',
              category: 'Insurance',
              required: true,
              uploaded: false,
            });
          }

          setRequiredDocuments(docs);
        }

        // Optional extras will be fetched from API when available
        // For now, start with empty array
        setOptionalExtras([]);
      } else {
        Alert.alert('Error', 'Quote not found');
        router.back();
      }
    }
  }, [quoteId, getQuoteById, setCurrentQuote]);

  // Handle back button
  const handleBack = () => {
    // Go back to the quotes list
    setTimeout(() => {
      try {
        router.push('/quotes');
      } catch (navError) {
        console.error('Navigation error:', navError);
        showToast(
          'error',
          'Navigation Error',
          'Failed to navigate back. Please try again.',
          { visibilityTime: 4000 }
        );
      }
    }, 100);
  };

  // Handle save
  const handleSave = async () => {
    if (!currentQuote) return;

    try {
      // Calculate new premium based on selected extras
      const extrasTotal = optionalExtras
        .filter(extra => extra.selected)
        .reduce((acc, extra) => acc + extra.price, 0);

      const newTotalPremium = totalPremium + extrasTotal;

      await updateQuote({
        premium: newTotalPremium,
        documents: requiredDocuments,
        additionalInfo: {
          ...currentQuote.additionalInfo,
          extras: optionalExtras,
          premiumBreakdown: {
            basePremium,
            items: premiumItems,
            totalPremium: newTotalPremium,
          },
        },
      });

      showToast(
        'success',
        'Quote Saved',
        'Your quote has been saved successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error saving quote:', error);
      Alert.alert('Error', 'Failed to save quote');
    }
  };

  // Check if all documents are verified
  const [allDocumentsVerified, setAllDocumentsVerified] = useState(true);

  // Update verification status when documents change
  useEffect(() => {
    // Get all document IDs
    const documentIds = requiredDocuments
      .filter(doc => doc.uploaded && doc.documentId)
      .map(doc => doc.documentId as string);

    // Check if all documents are verified
    const verified = areAllDocumentsVerified(documentIds);
    setAllDocumentsVerified(verified);
  }, [requiredDocuments, areAllDocumentsVerified, documentsInVerification]);

  // Get application store functions
  const { createApplicationFromQuote } = useApplicationStore();

  // Handle submit
  const handleSubmit = async () => {
    if (!currentQuote || !user) return;

    // Validate quote data for policy creation
    const validation = validateQuoteForPolicyCreation(currentQuote);
    if (!validation.isValid) {
      Alert.alert(
        'Validation Error',
        `Please fix the following issues:\n${validation.errors.join('\n')}`
      );
      return;
    }

    // Check if all required documents are uploaded
    const requiredDocumentsUploaded = requiredDocuments.every(doc => !doc.required || doc.uploaded);
    if (!requiredDocumentsUploaded) {
      showToast(
        'error',
        'Required Documents',
        'Please upload all required documents before submitting your quote',
        { visibilityTime: 4000 }
      );
      return;
    }

    // Check if all documents are verified
    if (!allDocumentsVerified) {
      showToast(
        'error',
        'Document Verification',
        'Please wait for all documents to be verified before submitting your quote',
        { visibilityTime: 4000 }
      );
      return;
    }

    try {
      // Calculate new premium based on selected extras
      const extrasTotal = optionalExtras
        .filter(extra => extra.selected)
        .reduce((acc, extra) => acc + extra.price, 0);

      const newTotalPremium = totalPremium + extrasTotal;

      // Update quote with final details
      const updatedQuote = {
        ...currentQuote,
        premium: newTotalPremium,
        currency: 'P', // Set Botswana currency
        documents: requiredDocuments,
        additionalInfo: {
          ...currentQuote.additionalInfo,
          extras: optionalExtras,
          premiumBreakdown: {
            basePremium,
            items: premiumItems,
            totalPremium: newTotalPremium,
          },
        },
        // PDF URL will be generated by the backend when quote is processed
        pdfUrl: undefined,
      };

      await updateQuote(updatedQuote);

      // Update quote status to 'quoted'
      await updateQuoteStatus(currentQuote.id, 'quoted');

      // Convert quote to policy and create it in the backend
      try {
        const policyData = convertQuoteToPolicy(updatedQuote, user.id || '');
        const createdPolicy = await dispatch(createPolicy(policyData));

        if (createdPolicy.meta.requestStatus === 'fulfilled') {
          showToast(
            'success',
            'Policy Created',
            'Your insurance policy has been created successfully',
            { visibilityTime: 3000 }
          );
        }
      } catch (policyError) {
        console.error('Error creating policy:', policyError);
        // Don't fail the entire process if policy creation fails
        showToast(
          'info',
          'Policy Creation',
          'Quote submitted successfully, but policy creation is pending. Please check your policies later.',
          { visibilityTime: 5000 }
        );
      }

      // Create an application from the quote
      const application = await createApplicationFromQuote(updatedQuote);

      showToast(
        'success',
        'Quote Submitted',
        'Your quote has been submitted successfully',
        { visibilityTime: 3000 }
      );

      // Use setTimeout to ensure navigation happens after component is mounted
      setTimeout(() => {
        try {
          // Navigate to the application tracker with the new application ID
          router.push({
            pathname: '/(app)/applications/[id]',
            params: { id: application.id }
          });
        } catch (navError) {
          console.error('Navigation error:', navError);
          showToast(
            'error',
            'Navigation Error',
            'Failed to navigate to application details. Please check the Applications tab.',
            { visibilityTime: 4000 }
          );
        }
      }, 100);
    } catch (error) {
      console.error('Error submitting quote:', error);
      Alert.alert('Error', 'Failed to submit quote');
    }
  };

  // Handle document upload
  const handleDocumentUpload = (documentType: string) => {
    // Find the document
    const docIndex = requiredDocuments.findIndex(doc => doc.type === documentType);

    if (docIndex !== -1) {
      // Update the document status
      const updatedDocs = [...requiredDocuments];

      // Get the document from the context if it exists
      const verifiedDocument = getVerifiedDocumentByType(documentType);

      if (verifiedDocument) {
        // If a verified document exists, use it
        updatedDocs[docIndex] = {
          ...updatedDocs[docIndex],
          uploaded: true,
          documentId: verifiedDocument.id,
          status: 'verified',
          uploadDate: new Date().toISOString().split('T')[0],
        };

        // Show success message
        showToast(
          'success',
          'Document Reused',
          `${updatedDocs[docIndex].name} has been automatically added from your verified documents`,
          { visibilityTime: 3000 }
        );
      } else {
        // Otherwise just mark as uploaded
        updatedDocs[docIndex] = {
          ...updatedDocs[docIndex],
          uploaded: true,
        };

        // Show success message
        showToast(
          'success',
          'Document Uploaded',
          `${updatedDocs[docIndex].name} has been uploaded successfully`,
          { visibilityTime: 3000 }
        );
      }

      setRequiredDocuments(updatedDocs);
    }
  };

  // Handle toggling an extra
  const handleToggleExtra = (extraId: string, selected: boolean) => {
    const updatedExtras = optionalExtras.map(extra =>
      extra.id === extraId ? { ...extra, selected } : extra
    );

    setOptionalExtras(updatedExtras);
  };

  // Define form steps
  const steps = [
    { id: '1', title: 'Client Info' },
    { id: '2', title: 'Quote Details' },
    { id: '3', title: 'Review' },
  ];

  const styles = StyleSheet.create({
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
      marginTop: spacing.lg,
    },
    clientInfoContainer: {
      backgroundColor: colors.card,
      borderRadius: 8,
      padding: spacing.md,
      marginBottom: spacing.lg,
    },
    clientInfoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    clientInfoLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
    },
    clientInfoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      textAlign: 'right',
    },
  });

  return (
    <QuoteFormContainer
      title="Quote Summary"
      subtitle="Review your quote details before submitting"
      steps={steps}
      currentStep={2}
      completedSteps={[0, 1]}
      onBack={handleBack}
      onSave={handleSave}
      onSubmit={handleSubmit}
      isLastStep={true}
      isLoading={isLoading}
      submitDisabled={!allDocumentsVerified}
      submitButtonText={allDocumentsVerified ? "Proceed " : "Waiting for Document Verification..."}
    >
      {currentQuote && (
        <>
          <Text style={styles.sectionTitle}>Client Information</Text>
          <View style={styles.clientInfoContainer}>
            <View style={styles.clientInfoRow}>
              <Text style={styles.clientInfoLabel}>Name</Text>
              <Text style={styles.clientInfoValue}>
                {currentQuote.clientInfo.firstName} {currentQuote.clientInfo.lastName}
              </Text>
            </View>
            <View style={styles.clientInfoRow}>
              <Text style={styles.clientInfoLabel}>Email</Text>
              <Text style={styles.clientInfoValue}>{currentQuote.clientInfo.email}</Text>
            </View>
            <View style={styles.clientInfoRow}>
              <Text style={styles.clientInfoLabel}>Phone</Text>
              <Text style={styles.clientInfoValue}>{currentQuote.clientInfo.phone}</Text>
            </View>
            {currentQuote.clientInfo.address && (
              <View style={styles.clientInfoRow}>
                <Text style={styles.clientInfoLabel}>Address</Text>
                <Text style={styles.clientInfoValue}>{currentQuote.clientInfo.address}</Text>
              </View>
            )}
          </View>

          <PremiumCalculationBreakdown
            basePremium={basePremium}
            items={premiumItems}
            totalPremium={totalPremium}
            currency={currentQuote.currency}
          />

          <OptionalExtrasSelector
            extras={optionalExtras}
            onToggleExtra={handleToggleExtra}
            currency={currentQuote.currency}
          />

          <RequiredDocumentsList
            documents={requiredDocuments}
            onUpload={handleDocumentUpload}
          />
        </>
      )}
    </QuoteFormContainer>
  );
}
