import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Share
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { ArrowLeft, Play, Share2, CheckCircle, XCircle, AlertCircle } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';
import { backendDiagnostics, DiagnosticReport, DiagnosticResult } from '@/utils/backendDiagnostics';

export default function BackendDiagnosticsScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const [isRunning, setIsRunning] = useState(false);
  const [report, setReport] = useState<DiagnosticReport | null>(null);

  const runDiagnostics = async () => {
    setIsRunning(true);
    setReport(null);
    
    try {
      const diagnosticReport = await backendDiagnostics.runAllTests();
      setReport(diagnosticReport);
    } catch (error) {
      console.error('Error running diagnostics:', error);
      Alert.alert(
        'Diagnostics Error',
        'Failed to run backend diagnostics. Check console for details.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsRunning(false);
    }
  };

  const shareReport = async () => {
    if (!report) return;
    
    const reportText = `
Backend Diagnostics Report
Generated: ${report.summary.timestamp}

Summary:
- Total Tests: ${report.summary.total}
- Passed: ${report.summary.passed}
- Failed: ${report.summary.failed}

Test Results:
${report.results.map(result => 
  `${result.success ? '✅' : '❌'} ${result.test}: ${result.success ? 'PASS' : 'FAIL'}${result.error ? ` (${result.error})` : ''}`
).join('\n')}

Recommendations:
${report.recommendations.map(rec => `• ${rec}`).join('\n')}

Technical Details:
${JSON.stringify(report.results, null, 2)}
    `.trim();

    try {
      await Share.share({
        message: reportText,
        title: 'Backend Diagnostics Report'
      });
    } catch (error) {
      console.error('Error sharing report:', error);
    }
  };

  const getStatusIcon = (success: boolean) => {
    if (success) {
      return <CheckCircle size={20} color={colors.success} />;
    } else {
      return <XCircle size={20} color={colors.error} />;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: 16,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      flex: 1,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    infoCard: {
      padding: 16,
      borderRadius: 12,
      marginBottom: 20,
      borderWidth: 1,
      borderColor: colors.border,
    },
    infoText: {
      fontSize: 14,
      lineHeight: 20,
      color: colors.textSecondary,
    },
    runButton: {
      backgroundColor: colors.primary[500],
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 12,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 20,
    },
    runButtonDisabled: {
      backgroundColor: colors.gray[300],
    },
    runButtonText: {
      color: colors.white,
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    reportContainer: {
      flex: 1,
    },
    summaryCard: {
      padding: 16,
      borderRadius: 12,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    summaryTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 12,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    summaryLabel: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    summaryValue: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
    },
    testResult: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    testResultSuccess: {
      backgroundColor: colors.success + '10',
      borderColor: colors.success + '30',
    },
    testResultFailed: {
      backgroundColor: colors.error + '10',
      borderColor: colors.error + '30',
    },
    testName: {
      flex: 1,
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
      marginLeft: 12,
    },
    testError: {
      fontSize: 12,
      color: colors.error,
      marginTop: 4,
      marginLeft: 32,
    },
    recommendationsCard: {
      padding: 16,
      borderRadius: 12,
      marginTop: 16,
      borderWidth: 1,
      borderColor: colors.warning + '30',
      backgroundColor: colors.warning + '10',
    },
    recommendationsTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 12,
      flexDirection: 'row',
      alignItems: 'center',
    },
    recommendation: {
      fontSize: 14,
      color: colors.text,
      marginBottom: 8,
      paddingLeft: 16,
    },
    shareButton: {
      backgroundColor: colors.secondary[500],
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 16,
    },
    shareButtonText: {
      color: colors.white,
      fontSize: 14,
      fontWeight: '600',
      marginLeft: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Backend Diagnostics</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={[styles.infoCard, { backgroundColor: colors.primary[50] }]}>
          <Text style={styles.infoText}>
            This tool runs comprehensive diagnostics to identify backend integration issues. 
            It will test connectivity, authentication, CORS, and request logging to help 
            determine why the backend developer is not receiving logs from the app.
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.runButton, isRunning && styles.runButtonDisabled]}
          onPress={runDiagnostics}
          disabled={isRunning}
        >
          <Play size={20} color={colors.white} />
          <Text style={styles.runButtonText}>
            {isRunning ? 'Running Diagnostics...' : 'Run Backend Diagnostics'}
          </Text>
        </TouchableOpacity>

        {report && (
          <View style={styles.reportContainer}>
            {/* Summary */}
            <View style={styles.summaryCard}>
              <Text style={styles.summaryTitle}>Summary</Text>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Total Tests:</Text>
                <Text style={styles.summaryValue}>{report.summary.total}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Passed:</Text>
                <Text style={[styles.summaryValue, { color: colors.success }]}>
                  {report.summary.passed}
                </Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Failed:</Text>
                <Text style={[styles.summaryValue, { color: colors.error }]}>
                  {report.summary.failed}
                </Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Generated:</Text>
                <Text style={styles.summaryValue}>
                  {new Date(report.summary.timestamp).toLocaleString()}
                </Text>
              </View>
            </View>

            {/* Test Results */}
            <Text style={styles.summaryTitle}>Test Results</Text>
            {report.results.map((result, index) => (
              <View
                key={index}
                style={[
                  styles.testResult,
                  result.success ? styles.testResultSuccess : styles.testResultFailed
                ]}
              >
                {getStatusIcon(result.success)}
                <View style={{ flex: 1 }}>
                  <Text style={styles.testName}>{result.test}</Text>
                  {result.error && (
                    <Text style={styles.testError}>{result.error}</Text>
                  )}
                </View>
              </View>
            ))}

            {/* Recommendations */}
            {report.recommendations.length > 0 && (
              <View style={styles.recommendationsCard}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                  <AlertCircle size={20} color={colors.warning} />
                  <Text style={[styles.summaryTitle, { marginBottom: 0, marginLeft: 8 }]}>
                    Recommendations
                  </Text>
                </View>
                {report.recommendations.map((recommendation, index) => (
                  <Text key={index} style={styles.recommendation}>
                    • {recommendation}
                  </Text>
                ))}
              </View>
            )}

            {/* Share Button */}
            <TouchableOpacity style={styles.shareButton} onPress={shareReport}>
              <Share2 size={16} color={colors.white} />
              <Text style={styles.shareButtonText}>Share Report</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
