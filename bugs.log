Starting Metro Bundler
Android Bundling failed 214ms node_modules/expo-router/entry.js (1 module)
SyntaxError: node_modules/expo-router/entry.js: Cannot find module '@babel/plugin-proposal-class-properties'
Require stack:
- /home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/files/plugins.js
- /home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/files/index.js
- /home/<USER>/workingdir/build/node_modules/@babel/core/lib/index.js
- /home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transform-worker/metro-transform-worker.js
- /home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transform-worker/transform-worker.js
- /home/<USER>/workingdir/build/node_modules/metro/src/DeltaBundler/Worker.flow.js
- /home/<USER>/workingdir/build/node_modules/metro/src/DeltaBundler/Worker.js
- /home/<USER>/workingdir/build/node_modules/jest-worker/build/workers/processChild.js
- Did you mean "@babel/plugin-transform-class-properties"?

Make sure that all the Babel plugins and presets you are using
are defined as dependencies or devDependencies in your package.json
file. It's possible that the missing plugin is loaded by a preset
you are using that forgot to add the plugin to its dependencies: you
can workaround this problem by explicitly adding the missing package
to your top-level package.json.

Error: Cannot find module '@babel/plugin-proposal-class-properties'
Require stack:
- /home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/files/plugins.js
- /home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/files/index.js
- /home/<USER>/workingdir/build/node_modules/@babel/core/lib/index.js
- /home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transform-worker/metro-transform-worker.js
- /home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transform-worker/transform-worker.js
- /home/<USER>/workingdir/build/node_modules/metro/src/DeltaBundler/Worker.flow.js
- /home/<USER>/workingdir/build/node_modules/metro/src/DeltaBundler/Worker.js
- /home/<USER>/workingdir/build/node_modules/jest-worker/build/workers/processChild.js
- Did you mean "@babel/plugin-transform-class-properties"?

Make sure that all the Babel plugins and presets you are using
are defined as dependencies or devDependencies in your package.json
file. It's possible that the missing plugin is loaded by a preset
you are using that forgot to add the plugin to its dependencies: you
can workaround this problem by explicitly adding the missing package
to your top-level package.json.

    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)
    at resolve (node:internal/modules/helpers:193:19)
    at tryRequireResolve (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/files/plugins.js:127:11)
    at resolveStandardizedNameForRequire (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/files/plugins.js:161:19)
    at resolveStandardizedName (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/files/plugins.js:182:12)
    at loadPlugin (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/files/plugins.js:55:7)
    at loadPlugin.next (<anonymous>)
    at createDescriptor (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-descriptors.js:140:16)
    at createDescriptor.next (<anonymous>)
    at evaluateSync (/home/<USER>/workingdir/build/node_modules/gensync/index.js:251:28)
    at /home/<USER>/workingdir/build/node_modules/gensync/index.js:31:34
    at Array.map (<anonymous>)
    at Function.sync (/home/<USER>/workingdir/build/node_modules/gensync/index.js:31:22)
    at Function.all (/home/<USER>/workingdir/build/node_modules/gensync/index.js:210:24)
    at Generator.next (<anonymous>)
    at createDescriptors (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-descriptors.js:102:41)
    at createDescriptors.next (<anonymous>)
    at createPluginDescriptors (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-descriptors.js:99:17)
    at createPluginDescriptors.next (<anonymous>)
    at /home/<USER>/workingdir/build/node_modules/@babel/core/lib/gensync-utils/functional.js:22:27
    at Generator.next (<anonymous>)
    at mergeChainOpts (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-chain.js:349:34)
    at mergeChainOpts.next (<anonymous>)
    at chainWalker (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-chain.js:316:14)
    at chainWalker.next (<anonymous>)
    at loadFileChain (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-chain.js:191:24)
    at loadFileChain.next (<anonymous>)
    at mergeExtendsChain (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-chain.js:328:28)
    at mergeExtendsChain.next (<anonymous>)
    at chainWalker (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-chain.js:312:20)
    at chainWalker.next (<anonymous>)
    at buildRootChain (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/config-chain.js:56:36)
    at buildRootChain.next (<anonymous>)
    at loadPrivatePartialConfig (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/partial.js:72:62)
    at loadPrivatePartialConfig.next (<anonymous>)
    at loadFullConfig (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/config/full.js:36:46)
    at loadFullConfig.next (<anonymous>)
    at transform (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/transform.js:20:44)
    at transform.next (<anonymous>)
    at evaluateSync (/home/<USER>/workingdir/build/node_modules/gensync/index.js:251:28)
    at sync (/home/<USER>/workingdir/build/node_modules/gensync/index.js:89:14)
    at stopHiding - secret - don't use this - v1 (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js:47:12)
    at Object.transformSync (/home/<USER>/workingdir/build/node_modules/@babel/core/lib/transform.js:42:76)
    at parseWithBabel (/home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transformSync.js:75:18)
    at transformSync (/home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transformSync.js:64:12)
    at Object.transform (/home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/babel-transformer.js:118:58)
    at transformJSWithBabel (/home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transform-worker/metro-transform-worker.js:470:47)
    at Object.transform (/home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transform-worker/metro-transform-worker.js:585:12)
    at Object.transform (/home/<USER>/workingdir/build/node_modules/@expo/metro-config/build/transform-worker/transform-worker.js:175:23)
