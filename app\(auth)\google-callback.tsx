import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

export default function GoogleCallbackScreen() {
  const { code, error } = useLocalSearchParams<{ code?: string; error?: string }>();
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing } = createTheme(isDarkMode);

  useEffect(() => {
    const handleGoogleCallback = async () => {
      console.log('[GoogleCallback] Processing OAuth callback');

      if (error) {
        console.log('[GoogleCallback] OAuth error:', error);
        // Show error and redirect to login
        setTimeout(() => {
          router.replace('/(auth)/login');
        }, 2000);
        return;
      }

      if (code) {
        console.log('[GoogleCallback] Authorization code received, processing...');

        try {
          // Import the auth service to handle the token exchange
          const { apiService } = await import('@/services/api');

          // Exchange the authorization code for user data (backend handles token securely)
          const response = await apiService.auth.googleCallback(code);

          if (response?.user) {
            console.log('[GoogleCallback] User authenticated successfully');

            // Store user data and redirect to app
            const AsyncStorage = await import('@react-native-async-storage/async-storage');
            const uuid = await import('react-native-uuid');

            const sessionToken = response.token || `google-session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

            await AsyncStorage.default.setItem('token', sessionToken);
            await AsyncStorage.default.setItem('user', JSON.stringify(response.user));

            // Redirect to app
            router.replace('/(app)/(tabs)');
          } else {
            console.log('[GoogleCallback] No user data received');
            router.replace('/(auth)/login');
          }
        } catch (error) {
          console.error('[GoogleCallback] Error processing callback:', error);
          router.replace('/(auth)/login');
        }
      } else {
        // No code or error, redirect to login
        console.log('[GoogleCallback] No authorization code received');
        router.replace('/(auth)/login');
      }
    };

    // Add a small delay to show the processing screen
    setTimeout(handleGoogleCallback, 1000);
  }, [code, error]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      alignItems: 'center',
      justifyContent: 'center',
    },
    content: {
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      marginTop: spacing.lg,
      marginBottom: spacing.md,
    },
    subtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      
      <View style={styles.content}>
        <ActivityIndicator size="large" color={colors.primary[500]} />
        <Text style={styles.title}>Processing Google Sign-in</Text>
        <Text style={styles.subtitle}>
          Please wait while we complete your Google sign-in...
        </Text>
      </View>
    </SafeAreaView>
  );
}
