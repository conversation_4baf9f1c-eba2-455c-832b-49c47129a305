/**
 * Basic setup test to verify Jest configuration
 */

describe('Test Setup Verification', () => {
  it('should run basic test', () => {
    expect(1 + 1).toBe(2);
  });

  it('should have access to mocked modules', () => {
    expect(jest).toBeDefined();
    expect(global.fetch).toBeDefined();
  });

  it('should handle async operations', async () => {
    const promise = Promise.resolve('test');
    const result = await promise;
    expect(result).toBe('test');
  });

  it('should have proper environment setup', () => {
    expect(process.env.EXPO_PUBLIC_API_BASE_URL).toBeDefined();
  });
});
