import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, Plus, Filter } from 'lucide-react-native';
import { router } from 'expo-router';
import usePaymentStore from '@/store/paymentStore';
import PaymentHistoryView from '@/components/payments/PaymentHistoryView';
import PaymentFilterModal from '@/components/payments/PaymentFilterModal';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';
import { useVerifiedDocuments } from '@/context/OptimizedUploadContext';
import { PaymentHistoryItem, PaymentFilters } from '@/types/payment.types';

export default function PaymentHistoryScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const { documents: verifiedDocuments } = useVerifiedDocuments();
  const pendingDocuments: any[] = []; // Mock for compatibility

  // Get payment store methods
  const {
    paymentHistory,
    fetchPaymentHistory,
    getPaymentsByFilter,
    paymentProofs,
    fetchPaymentProofs,
    isLoading
  } = usePaymentStore();

  // State
  const [payments, setPayments] = useState<PaymentHistoryItem[]>([]);
  const [isLoadingPayments, setIsLoadingPayments] = useState(false);
  const [filters, setFilters] = useState<PaymentFilters>({});
  const [showFilterModal, setShowFilterModal] = useState(false);

  // Load payment history and proofs
  useEffect(() => {
    const loadPayments = async () => {
      setIsLoadingPayments(true);
      try {
        // Fetch payment history from store
        await fetchPaymentHistory();
        await fetchPaymentProofs();

        // If we have payment history, use it
        if (paymentHistory.length > 0) {
          setPayments(getPaymentsByFilter(filters));
        } else {
          // Create empty payments array - in a real app, this would be populated from the API
          setPayments([]);

          // Show toast to inform user
          showToast(
            'info',
            'Using Local Data',
            'Payment history is being loaded from local storage',
            { visibilityTime: 3000 }
          );
        }
      } catch (error) {
        console.error('Error loading payments:', error);
        showToast(
          'error',
          'Error',
          'Failed to load payment history. Please try again.',
          { visibilityTime: 3000 }
        );
      } finally {
        setIsLoadingPayments(false);
      }
    };

    loadPayments();
  }, [filters, fetchPaymentHistory, fetchPaymentProofs, getPaymentsByFilter, paymentHistory]);

  // Handle view payment
  const handleViewPayment = (paymentId: string) => {
    // Navigate to payment details screen
    router.push({
      pathname: '/payment/[id]',
      params: { id: paymentId }
    });
  };

  // Handle view receipt
  const handleViewReceipt = async (receiptId: string) => {
    try {
      // Find document in document upload context
      const allDocuments = [...pendingDocuments, ...verifiedDocuments];
      const document = allDocuments.find(doc =>
        doc.type === 'Payment' &&
        doc.metadata &&
        doc.metadata.paymentId === receiptId
      );

      if (document) {
        // Open document
        showToast(
          'success',
          'Receipt Opened',
          'Your receipt has been opened in a new window',
          { visibilityTime: 3000 }
        );
      } else {
        throw new Error('Receipt not found');
      }
    } catch (error) {
      console.error('Error viewing receipt:', error);
      showToast(
        'error',
        'Receipt Viewing Failed',
        'Failed to open receipt. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  // Handle upload proof
  const handleUploadProof = (paymentId: string) => {
    // Navigate to payment verification screen
    router.push({
      pathname: '/payment/verify',
      params: { id: paymentId }
    });
  };

  // Handle new payment
  const handleNewPayment = () => {
    router.push('/payment/new');
  };

  // Handle filter change
  const handleFilterChange = (newFilters: PaymentFilters) => {
    setFilters(newFilters);
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment History</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[
              styles.iconButton,
              Object.keys(filters).length > 0 && styles.activeIconButton
            ]}
            onPress={() => setShowFilterModal(true)}
            activeOpacity={0.7}
          >
            {Object.keys(filters).length > 0 ? (
              <View style={styles.filterBadgeContainer}>
                <Filter size={22} color={colors.white} />
                <View style={styles.filterBadge}>
                  <Text style={styles.filterBadgeText}>
                    {Object.keys(filters).length}
                  </Text>
                </View>
              </View>
            ) : (
              <Filter size={22} color={colors.primary[500]} />
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={handleNewPayment}
            activeOpacity={0.7}
          >
            <Plus size={24} color={colors.primary[500]} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.content}>
        <PaymentHistoryView
          payments={payments}
          paymentProofs={paymentProofs}
          isLoading={isLoadingPayments || isLoading}
          onViewPayment={handleViewPayment}
          onViewReceipt={handleViewReceipt}
          onUploadProof={handleUploadProof}
          onFilterChange={handleFilterChange}
        />
      </View>

      <BottomNavBar currentRoute="payments" />

      {/* Payment Filter Modal */}
      <PaymentFilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApplyFilters={handleFilterChange}
        initialFilters={filters}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    borderRadius: 20,
  },
  activeIconButton: {
    backgroundColor: '#6200ee',
  },
  filterBadgeContainer: {
    position: 'relative',
  },
  filterBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#ff5722',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
});
