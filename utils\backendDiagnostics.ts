/**
 * Backend Diagnostics Utility
 * 
 * This utility provides comprehensive diagnostics for backend integration issues
 * and helps identify why the backend dev is not receiving logs from the app.
 */

import axios, { AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from '@/services/api';

export interface DiagnosticResult {
  test: string;
  success: boolean;
  error?: string;
  details?: any;
  timestamp: string;
}

export interface DiagnosticReport {
  summary: {
    total: number;
    passed: number;
    failed: number;
    timestamp: string;
  };
  results: DiagnosticResult[];
  recommendations: string[];
}

export class BackendDiagnostics {
  private results: DiagnosticResult[] = [];
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL || 'https://inerca-backend-wild-leaf-8326.fly.dev';
    console.log('[BackendDiagnostics] Initialized with base URL:', this.baseUrl);
  }

  private addResult(test: string, success: boolean, error?: string, details?: any) {
    const result: DiagnosticResult = {
      test,
      success,
      error,
      details,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    console.log(`[BackendDiagnostics] ${test}: ${success ? 'PASS' : 'FAIL'}`, error || '');
  }

  /**
   * Test 1: Basic URL Connectivity
   */
  async testUrlConnectivity(): Promise<void> {
    try {
      console.log('[BackendDiagnostics] Testing URL connectivity to:', this.baseUrl);
      
      const response = await fetch(this.baseUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Inerca-Mobile-App/1.0.0'
        }
      });

      this.addResult(
        'URL Connectivity',
        response.status < 500,
        response.status >= 500 ? `HTTP ${response.status}` : undefined,
        {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries())
        }
      );
    } catch (error) {
      this.addResult(
        'URL Connectivity',
        false,
        error instanceof Error ? error.message : 'Unknown error',
        { error }
      );
    }
  }

  /**
   * Test 2: Health Check Endpoint
   */
  async testHealthEndpoint(): Promise<void> {
    try {
      console.log('[BackendDiagnostics] Testing health endpoint');
      
      const response = await apiService.health.check();
      
      this.addResult(
        'Health Check Endpoint',
        true,
        undefined,
        response
      );
    } catch (error) {
      const axiosError = error as AxiosError;
      this.addResult(
        'Health Check Endpoint',
        false,
        axiosError.message,
        {
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data,
          config: {
            url: axiosError.config?.url,
            method: axiosError.config?.method,
            headers: axiosError.config?.headers
          }
        }
      );
    }
  }

  /**
   * Test 3: CORS and Preflight Requests
   */
  async testCorsAndPreflight(): Promise<void> {
    try {
      console.log('[BackendDiagnostics] Testing CORS and preflight requests');
      
      // Test OPTIONS request (preflight)
      const response = await fetch(`${this.baseUrl}/api/v1/health`, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'http://localhost:8081',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type,Authorization'
        }
      });

      const corsHeaders = {
        'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
        'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
        'access-control-allow-headers': response.headers.get('access-control-allow-headers'),
        'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
      };

      this.addResult(
        'CORS and Preflight',
        response.status === 200 || response.status === 204,
        response.status >= 400 ? `HTTP ${response.status}` : undefined,
        {
          status: response.status,
          corsHeaders,
          allHeaders: Object.fromEntries(response.headers.entries())
        }
      );
    } catch (error) {
      this.addResult(
        'CORS and Preflight',
        false,
        error instanceof Error ? error.message : 'Unknown error',
        { error }
      );
    }
  }

  /**
   * Test 4: Authentication Headers
   */
  async testAuthenticationHeaders(): Promise<void> {
    try {
      console.log('[BackendDiagnostics] Testing authentication headers');
      
      // Get stored token
      const token = await AsyncStorage.getItem('auth_token');
      
      // Test with and without token
      const testCases = [
        { name: 'Without Token', token: null },
        { name: 'With Token', token: token || 'test-token' }
      ];

      for (const testCase of testCases) {
        try {
          const headers: Record<string, string> = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Inerca-Mobile-App/1.0.0'
          };

          if (testCase.token) {
            headers['Authorization'] = `Bearer ${testCase.token}`;
          }

          const response = await fetch(`${this.baseUrl}/api/v1/user/me`, {
            method: 'GET',
            headers
          });

          this.addResult(
            `Authentication Headers (${testCase.name})`,
            response.status === 200 || response.status === 401, // 401 is expected without valid token
            response.status >= 500 ? `HTTP ${response.status}` : undefined,
            {
              status: response.status,
              hasToken: !!testCase.token,
              tokenLength: testCase.token?.length || 0
            }
          );
        } catch (error) {
          this.addResult(
            `Authentication Headers (${testCase.name})`,
            false,
            error instanceof Error ? error.message : 'Unknown error',
            { error, hasToken: !!testCase.token }
          );
        }
      }
    } catch (error) {
      this.addResult(
        'Authentication Headers',
        false,
        error instanceof Error ? error.message : 'Unknown error',
        { error }
      );
    }
  }

  /**
   * Test 5: Request Logging Test
   */
  async testRequestLogging(): Promise<void> {
    try {
      console.log('[BackendDiagnostics] Testing request logging with unique identifier');
      
      const uniqueId = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const response = await fetch(`${this.baseUrl}/api/v1/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Request-ID': uniqueId,
          'X-Client': 'Inerca-Mobile-App',
          'X-Version': '1.0.0',
          'User-Agent': 'Inerca-Mobile-App/1.0.0'
        }
      });

      this.addResult(
        'Request Logging Test',
        response.status < 500,
        response.status >= 500 ? `HTTP ${response.status}` : undefined,
        {
          uniqueId,
          status: response.status,
          message: `Backend dev should look for request with ID: ${uniqueId}`
        }
      );
    } catch (error) {
      this.addResult(
        'Request Logging Test',
        false,
        error instanceof Error ? error.message : 'Unknown error',
        { error }
      );
    }
  }

  /**
   * Test 6: Network Configuration
   */
  async testNetworkConfiguration(): Promise<void> {
    try {
      console.log('[BackendDiagnostics] Testing network configuration');
      
      const networkInfo = {
        baseUrl: this.baseUrl,
        envVar: process.env.EXPO_PUBLIC_API_BASE_URL,
        userAgent: 'Inerca-Mobile-App/1.0.0',
        timestamp: new Date().toISOString()
      };

      // Test DNS resolution
      const dnsTest = await fetch(this.baseUrl, { method: 'HEAD' });
      
      this.addResult(
        'Network Configuration',
        true,
        undefined,
        {
          ...networkInfo,
          dnsResolution: dnsTest.status < 500 ? 'OK' : 'FAILED'
        }
      );
    } catch (error) {
      this.addResult(
        'Network Configuration',
        false,
        error instanceof Error ? error.message : 'Unknown error',
        {
          baseUrl: this.baseUrl,
          envVar: process.env.EXPO_PUBLIC_API_BASE_URL,
          error
        }
      );
    }
  }

  /**
   * Run all diagnostic tests
   */
  async runAllTests(): Promise<DiagnosticReport> {
    console.log('[BackendDiagnostics] Starting comprehensive backend diagnostics...');
    
    this.results = []; // Clear previous results
    
    // Run all tests
    await this.testUrlConnectivity();
    await this.testHealthEndpoint();
    await this.testCorsAndPreflight();
    await this.testAuthenticationHeaders();
    await this.testRequestLogging();
    await this.testNetworkConfiguration();

    // Generate report
    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.length - passed;

    const recommendations = this.generateRecommendations();

    const report: DiagnosticReport = {
      summary: {
        total: this.results.length,
        passed,
        failed,
        timestamp: new Date().toISOString()
      },
      results: this.results,
      recommendations
    };

    console.log('[BackendDiagnostics] Diagnostics complete:', report.summary);
    return report;
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    const failedTests = this.results.filter(r => !r.success);
    
    if (failedTests.some(t => t.test === 'URL Connectivity')) {
      recommendations.push('Check if the backend server is running and accessible');
      recommendations.push('Verify the backend URL in environment variables');
    }
    
    if (failedTests.some(t => t.test.includes('CORS'))) {
      recommendations.push('Backend needs to configure CORS headers for mobile app requests');
      recommendations.push('Add mobile app origins to CORS allowed origins');
    }
    
    if (failedTests.some(t => t.test === 'Health Check Endpoint')) {
      recommendations.push('Health endpoint may not be implemented correctly on backend');
    }
    
    if (failedTests.some(t => t.test.includes('Authentication'))) {
      recommendations.push('Check authentication token format and validation on backend');
    }
    
    recommendations.push('Ask backend dev to check logs for requests with X-Request-ID headers');
    recommendations.push('Ensure backend logging is enabled for incoming requests');
    
    return recommendations;
  }
}

export const backendDiagnostics = new BackendDiagnostics();
