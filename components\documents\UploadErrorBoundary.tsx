import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { AlertTriangle, RefreshCw, Camera, Upload } from 'lucide-react-native';
import { createTheme } from '@/constants/theme';
import Toast from 'react-native-toast-message';
import { UploadManager } from '@/utils/UploadManager';

interface UploadErrorBoundaryProps {
  children: ReactNode;
  onRetry?: () => void;
  uploadType?: 'camera' | 'gallery' | 'document';
}

interface UploadErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
}

/**
 * Specialized Error Boundary for upload operations
 * Provides specific error handling and recovery for camera/file upload issues
 */
class UploadErrorBoundary extends Component<UploadErrorBoundaryProps, UploadErrorBoundaryState> {
  private maxRetries = 3;
  private retryTimeout: NodeJS.Timeout | null = null;

  constructor(props: UploadErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<UploadErrorBoundaryState> {
    console.error('[UploadErrorBoundary] Upload error caught:', error);
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[UploadErrorBoundary] Upload error details:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      uploadType: this.props.uploadType,
      retryCount: this.state.retryCount,
    });

    // Clear any pending uploads
    this.cleanupUploads();

    // Show appropriate error message
    this.showErrorToast(error);
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
    this.cleanupUploads();
  }

  private cleanupUploads = () => {
    try {
      // Clear any pending uploads in the upload manager
      UploadManager.clearCompletedUploads();
    } catch (error) {
      console.error('[UploadErrorBoundary] Error during cleanup:', error);
    }
  };

  private showErrorToast = (error: Error) => {
    const message = this.getErrorMessage(error);
    
    Toast.show({
      type: 'error',
      text1: 'Upload Error',
      text2: message,
      visibilityTime: 5000,
      onPress: () => Toast.hide(),
    });
  };

  private getErrorMessage = (error: Error): string => {
    const message = error.message.toLowerCase();
    const { uploadType } = this.props;
    
    // Camera-specific errors
    if (uploadType === 'camera') {
      if (message.includes('permission')) {
        return 'Camera permission denied. Please enable camera access in settings.';
      }
      if (message.includes('camera') || message.includes('unavailable')) {
        return 'Camera is not available. Please try using gallery instead.';
      }
      if (message.includes('hardware')) {
        return 'Camera hardware error. Please restart the app and try again.';
      }
    }
    
    // Gallery-specific errors
    if (uploadType === 'gallery') {
      if (message.includes('permission')) {
        return 'Photo library permission denied. Please enable photo access in settings.';
      }
      if (message.includes('library') || message.includes('photos')) {
        return 'Unable to access photo library. Please check permissions.';
      }
    }
    
    // Document picker errors
    if (uploadType === 'document') {
      if (message.includes('permission')) {
        return 'File access permission denied. Please enable file access in settings.';
      }
      if (message.includes('file') || message.includes('document')) {
        return 'Unable to access files. Please try a different file.';
      }
    }
    
    // General upload errors
    if (message.includes('network')) {
      return 'Network error. Please check your connection and try again.';
    }
    
    if (message.includes('memory') || message.includes('space')) {
      return 'Insufficient storage space. Please free up some space and try again.';
    }
    
    if (message.includes('size') || message.includes('large')) {
      return 'File is too large. Please select a smaller file.';
    }
    
    if (message.includes('format') || message.includes('type')) {
      return 'Unsupported file format. Please select a different file.';
    }
    
    return 'Upload failed. Please try again or contact support if the problem persists.';
  };

  private handleRetry = () => {
    const { retryCount } = this.state;
    const { onRetry } = this.props;
    
    if (retryCount >= this.maxRetries) {
      Toast.show({
        type: 'error',
        text1: 'Max Retries Reached',
        text2: 'Please restart the app or contact support.',
        visibilityTime: 5000,
      });
      return;
    }

    console.log(`[UploadErrorBoundary] Retrying upload (attempt ${retryCount + 1}/${this.maxRetries})`);
    
    this.setState(prevState => ({
      hasError: false,
      error: null,
      retryCount: prevState.retryCount + 1,
    }));

    // Call custom retry handler if provided
    if (onRetry) {
      this.retryTimeout = setTimeout(() => {
        onRetry();
      }, 1000); // Small delay to prevent immediate re-error
    }

    Toast.show({
      type: 'info',
      text1: 'Retrying Upload',
      text2: `Attempt ${retryCount + 1} of ${this.maxRetries}`,
      visibilityTime: 2000,
    });
  };

  private handleReset = () => {
    console.log('[UploadErrorBoundary] Resetting upload error boundary');
    
    this.setState({
      hasError: false,
      error: null,
      retryCount: 0,
    });

    this.cleanupUploads();
    
    Toast.show({
      type: 'success',
      text1: 'Reset Complete',
      text2: 'You can now try uploading again.',
      visibilityTime: 2000,
    });
  };

  private getUploadIcon = () => {
    const { uploadType } = this.props;
    const theme = createTheme(false);
    const iconColor = theme.colors.primary[500];
    
    switch (uploadType) {
      case 'camera':
        return <Camera size={32} color={iconColor} />;
      case 'gallery':
      case 'document':
        return <Upload size={32} color={iconColor} />;
      default:
        return <AlertTriangle size={32} color={theme.colors.error[500]} />;
    }
  };

  render() {
    const { hasError, error, retryCount } = this.state;
    const { children } = this.props;

    if (hasError && error) {
      const theme = createTheme(false);
      const { colors, spacing, typography } = theme;
      
      const canRetry = retryCount < this.maxRetries;

      const styles = StyleSheet.create({
        container: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: spacing.lg,
          backgroundColor: colors.background,
          minHeight: 200,
        },
        iconContainer: {
          marginBottom: spacing.lg,
        },
        title: {
          fontSize: typography.sizes.lg,
          fontWeight: typography.weights.semibold,
          color: colors.error[500],
          textAlign: 'center',
          marginBottom: spacing.sm,
        },
        message: {
          fontSize: typography.sizes.md,
          color: colors.textSecondary,
          textAlign: 'center',
          lineHeight: 22,
          marginBottom: spacing.lg,
          paddingHorizontal: spacing.md,
        },
        retryInfo: {
          fontSize: typography.sizes.sm,
          color: colors.gray[500],
          textAlign: 'center',
          marginBottom: spacing.lg,
        },
        buttonContainer: {
          flexDirection: 'row',
          gap: spacing.md,
        },
        button: {
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: spacing.md,
          paddingHorizontal: spacing.lg,
          borderRadius: 8,
          minWidth: 100,
        },
        retryButton: {
          backgroundColor: canRetry ? colors.primary[500] : colors.gray[300],
        },
        resetButton: {
          backgroundColor: colors.gray[200],
          borderWidth: 1,
          borderColor: colors.gray[300],
        },
        buttonText: {
          fontSize: typography.sizes.md,
          fontWeight: typography.weights.medium,
          marginLeft: spacing.sm,
        },
        retryButtonText: {
          color: canRetry ? colors.white : colors.gray[500],
        },
        resetButtonText: {
          color: colors.gray[700],
        },
      });

      return (
        <View style={styles.container}>
          <View style={styles.iconContainer}>
            {this.getUploadIcon()}
          </View>
          
          <Text style={styles.title}>Upload Failed</Text>
          
          <Text style={styles.message}>
            {this.getErrorMessage(error)}
          </Text>

          {retryCount > 0 && (
            <Text style={styles.retryInfo}>
              Retry attempt: {retryCount}/{this.maxRetries}
            </Text>
          )}

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.retryButton]}
              onPress={this.handleRetry}
              disabled={!canRetry}
              activeOpacity={0.8}
            >
              <RefreshCw size={18} color={canRetry ? colors.white : colors.gray[500]} />
              <Text style={[styles.buttonText, styles.retryButtonText]}>
                {canRetry ? 'Retry' : 'Max Retries'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.resetButton]}
              onPress={this.handleReset}
              activeOpacity={0.8}
            >
              <Upload size={18} color={colors.gray[700]} />
              <Text style={[styles.buttonText, styles.resetButtonText]}>
                Reset
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return children;
  }
}

export default UploadErrorBoundary;
