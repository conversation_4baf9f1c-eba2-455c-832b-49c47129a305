import React, { useState, useEffect } from 'react';
import { View, Alert, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { Car, Home, Calendar, Hash, Info, User, Briefcase, MapPin } from 'lucide-react-native';
import PulaIcon from '@/components/ui/PulaIcon';
import QuoteFormContainer from '@/components/quotes/QuoteFormContainer';
import DynamicFormField, { SelectOption } from '@/components/quotes/DynamicFormField';
import useQuoteStore from '@/store/quoteStore';
import { MotorQuoteInfo, HomeQuoteInfo } from '@/types/quote.types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';

export default function TypeSpecificQuoteScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const { type, quoteId } = useLocalSearchParams();

  // Get quote store functions
  const {
    getQuoteById,
    updateQuote,
    setCurrentQuote,
    currentQuote,
    isLoading: isQuoteLoading
  } = useQuoteStore();

  // Form state - will be populated based on quote type
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formValid, setFormValid] = useState(false);
  const [isStorageLoading, setIsStorageLoading] = useState(true);
  const [storedQuoteId, setStoredQuoteId] = useState<string | null>(null);
  const [storedQuoteType, setStoredQuoteType] = useState<string | null>(null);

  // First useEffect to check AsyncStorage for stored quote data
  useEffect(() => {
    const checkAsyncStorage = async () => {
      try {
        // Try to get the quoteId and type from AsyncStorage
        const [storedId, storedType] = await Promise.all([
          AsyncStorage.getItem('currentQuoteId'),
          AsyncStorage.getItem('currentQuoteType')
        ]);

        console.log('TypeSpecificQuoteScreen - Retrieved from AsyncStorage:', { storedId, storedType });

        if (storedId) setStoredQuoteId(storedId);
        if (storedType) setStoredQuoteType(storedType);

        setIsStorageLoading(false);
      } catch (error) {
        console.error('TypeSpecificQuoteScreen - AsyncStorage error:', error);
        setIsStorageLoading(false);
      }
    };

    checkAsyncStorage();
  }, []);

  // Load quote data
  useEffect(() => {
    // Skip if still loading AsyncStorage data
    if (isStorageLoading) return;

    console.log('TypeSpecificQuoteScreen - Params received:', { type, quoteId });
    console.log('TypeSpecificQuoteScreen - Stored data:', { storedQuoteId, storedQuoteType });

    // Handle case where parameters might be in the URL query string or AsyncStorage
    let quoteIdToUse = (quoteId as string) || storedQuoteId;
    let typeToUse = (type as string) || storedQuoteType;

    // If we don't have a type parameter, check if we can determine it from the route
    if (!typeToUse) {
      console.warn('No type parameter received directly');

      // We can't easily get the route info in Expo Router
      // So we'll try a different approach

      // Check if we can infer the type from the current screen name
      try {
        // In React Native, we can try to infer from the component name
        const screenName = __filename.split('\\').pop() || '';
        console.log('Current screen filename:', screenName);

        if (screenName.includes('motor')) {
          typeToUse = 'motor';
          console.log('Inferred type from screen name:', typeToUse);
        }
      } catch (err) {
        console.error('Error trying to infer type:', err);
      }

      // If we still don't have a type, check all params
      if (!typeToUse) {
        // Check if type is in any of the other params
        const allParams = useLocalSearchParams();
        console.log('All params:', allParams);

        if (allParams.type) {
          typeToUse = allParams.type as string;
          console.log('Found type in params:', typeToUse);
        } else {
          console.error('No type parameter found');
          Alert.alert('Error', 'Missing quote type information');
          router.replace('/(app)/quotes');
          return;
        }
      }
    }

    // If we don't have a quoteId parameter, check all params
    if (!quoteIdToUse) {
      console.warn('No quoteId parameter received directly');

      // Check if quoteId is in any of the other params
      const allParams = useLocalSearchParams();
      console.log('All params for quoteId:', allParams);

      if (allParams.quoteId) {
        quoteIdToUse = allParams.quoteId as string;
        console.log('Found quoteId in params:', quoteIdToUse);
      } else {
        console.error('No quoteId parameter found');
        Alert.alert('Error', 'Missing quote ID information');
        router.replace('/(app)/quotes');
        return;
      }
    }

    console.log('Looking up quote with ID:', quoteIdToUse);
    const quote = getQuoteById(quoteIdToUse);

    if (quote) {
      console.log('Quote found:', quote.id, 'Type:', quote.type);
      setCurrentQuote(quote);

      // Initialize form data based on quote type
      if (quote.additionalInfo) {
        console.log('Using existing additionalInfo:', quote.additionalInfo);
        setFormData(quote.additionalInfo);
      } else {
        console.log('No additionalInfo found, setting defaults for type:', quote.type);
        // Set default values based on quote type
        switch (quote.type) {
          case 'motor':
            setFormData({
              vehicleMake: '',
              vehicleModel: '',
              vehicleYear: new Date().getFullYear(),
              vehicleValue: 0,
              registrationNumber: '',
              usage: 'personal',
              coverType: 'comprehensive',
            });
            break;
          case 'houseowners':
            setFormData({
              propertyAddress: quote.clientInfo.address || '',
              constructionType: '',
              occupancyStatus: 'owner',
              buildingValue: 0,
              yearBuilt: new Date().getFullYear() - 10,
              securityFeatures: [],
              coverType: 'standard',
            });
            break;
          case 'householdContents':
            setFormData({
              propertyAddress: quote.clientInfo.address || '',
              securityFeatures: [],
              contentsValue: 0,
              highValueItems: [],
              powerSurgeProtection: false,
              accidentalDamage: false,
            });
            break;
          case 'allRisks':
            setFormData({
              unspecifiedItemsValue: 0,
              specifiedItems: [],
            });
            break;
          case 'life':
            setFormData({
              insuredPerson: {
                firstName: quote.clientInfo.firstName,
                lastName: quote.clientInfo.lastName,
                dateOfBirth: quote.clientInfo.dateOfBirth || '',
                gender: 'male',
                smoker: false,
                occupation: quote.clientInfo.occupation || '',
                healthConditions: [],
              },
              coverageAmount: 0,
              term: 20,
              beneficiaries: [],
            });
            break;
          case 'funeral':
            setFormData({
              insuredPerson: {
                firstName: quote.clientInfo.firstName,
                lastName: quote.clientInfo.lastName,
                dateOfBirth: quote.clientInfo.dateOfBirth || '',
                gender: 'male',
              },
              coverageAmount: 0,
              familyMembers: [],
              beneficiaries: [],
            });
            break;
          case 'scheme':
            setFormData({
              memberDetails: {
                firstName: quote.clientInfo.firstName,
                lastName: quote.clientInfo.lastName,
                dateOfBirth: quote.clientInfo.dateOfBirth || '',
                employeeNumber: '',
              },
              employerName: '',
              employerAddress: '',
              schemeType: '',
              supportingForms: [],
            });
            break;
          // Add other quote types as needed
          default:
            setFormData({});
        }
      }
    } else {
      Alert.alert('Error', 'Quote not found');
      router.replace('/(app)/quotes');
    }
  }, [quoteId, storedQuoteId, storedQuoteType, isStorageLoading, getQuoteById, setCurrentQuote]);

  // Validate form when data changes
  useEffect(() => {
    validateForm();
  }, [formData]);

  // Update form data
  const updateFormField = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form based on quote type
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!currentQuote) return;

    switch (currentQuote.type) {
      case 'motor':
        if (!formData.vehicleMake) {
          newErrors.vehicleMake = 'Vehicle make is required';
        }
        if (!formData.vehicleModel) {
          newErrors.vehicleModel = 'Vehicle model is required';
        }
        if (!formData.registrationNumber) {
          newErrors.registrationNumber = 'Registration number is required';
        }
        if (!formData.vehicleValue || formData.vehicleValue <= 0) {
          newErrors.vehicleValue = 'Vehicle value must be greater than 0';
        }
        break;

      case 'houseowners':
        if (!formData.constructionType) {
          newErrors.constructionType = 'Construction type is required';
        }
        if (!formData.propertyAddress) {
          newErrors.propertyAddress = 'Property address is required';
        }
        if (!formData.buildingValue || formData.buildingValue <= 0) {
          newErrors.buildingValue = 'Building value must be greater than 0';
        }
        break;

      case 'householdContents':
        if (!formData.propertyAddress) {
          newErrors.propertyAddress = 'Property address is required';
        }
        if (!formData.contentsValue || formData.contentsValue <= 0) {
          newErrors.contentsValue = 'Contents value must be greater than 0';
        }
        break;

      case 'allRisks':
        if ((!formData.unspecifiedItemsValue || formData.unspecifiedItemsValue <= 0) &&
            (!formData.specifiedItems || formData.specifiedItems.length === 0)) {
          newErrors.unspecifiedItemsValue = 'Either unspecified items value or specified items are required';
        }
        break;

      case 'life':
        if (!formData.insuredPerson?.dateOfBirth) {
          newErrors.dateOfBirth = 'Date of birth is required';
        }
        if (!formData.coverageAmount || formData.coverageAmount <= 0) {
          newErrors.coverageAmount = 'Coverage amount must be greater than 0';
        }
        break;

      case 'funeral':
        if (!formData.insuredPerson?.dateOfBirth) {
          newErrors.dateOfBirth = 'Date of birth is required';
        }
        if (!formData.coverageAmount || formData.coverageAmount <= 0) {
          newErrors.coverageAmount = 'Coverage amount must be greater than 0';
        }
        break;

      case 'scheme':
        if (!formData.memberDetails?.employeeNumber) {
          newErrors.employeeNumber = 'Employee number is required';
        }
        if (!formData.employerName) {
          newErrors.employerName = 'Employer name is required';
        }
        break;

      // Add validation for other quote types as needed
    }

    setErrors(newErrors);
    setFormValid(Object.keys(newErrors).length === 0);
  };

  // Handle back button
  const handleBack = () => {
    // Go back to the quotes list
    router.push('/(app)/quotes' as any);
  };

  // Handle save
  const handleSave = async () => {
    if (!currentQuote) return;

    try {
      await updateQuote({
        additionalInfo: formData,
        // Make sure the status is still draft
        status: 'draft',
        // Update the timestamp to ensure it's at the top of the list
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Get the fetchQuotes function from the store to refresh the quotes list
      const { fetchQuotes } = useQuoteStore.getState();

      // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
      await fetchQuotes();

      // Show a toast notification for better user feedback
      showToast(
        'success',
        'Draft Saved',
        'Your quote draft has been saved',
        { visibilityTime: 2000 }
      );
    } catch (error) {
      console.error('Error saving quote details:', error);
      showToast(
        'error',
        'Save Failed',
        'Failed to save quote details. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Handle next
  const handleNext = async () => {
    if (!currentQuote) return;

    try {
      // Save form data
      await updateQuote({
        additionalInfo: formData,
        // In a real app, you would calculate the premium here
        premium: Math.floor(Math.random() * 1000) + 500,
        coverAmount: formData.vehicleValue || formData.buildingValue || 50000,
        // Update the timestamp to ensure it's at the top of the list
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Get the fetchQuotes function from the store to refresh the quotes list
      const { fetchQuotes } = useQuoteStore.getState();

      // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
      await fetchQuotes();

      // Navigate to the summary page
      router.push({
        pathname: '/(app)/quotes/[type]/summary',
        params: {
          type: currentQuote.type,
          quoteId: currentQuote.id
        }
      });
    } catch (error) {
      console.error('Error saving quote details:', error);
      showToast(
        'error',
        'Save Failed',
        'Failed to save quote details. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Define form steps
  const steps = [
    { id: '1', title: 'Client Info' },
    { id: '2', title: 'Quote Details' },
    { id: '3', title: 'Review' },
  ];

  // Get form title based on quote type
  const getFormTitle = () => {
    if (!currentQuote) return 'Quote Details';

    switch (currentQuote.type) {
      case 'motor':
        return 'Motor Insurance Details';
      case 'houseowners':
        return 'Houseowners Insurance Details';
      case 'householdContents':
        return 'Household Contents Insurance Details';
      case 'allRisks':
        return 'All Risks Insurance Details';
      case 'travel':
        return 'Travel Insurance Details';
      case 'health':
        return 'Health Insurance Details';
      case 'life':
        return 'Life Assurance Details';
      case 'funeral':
        return 'Funeral Cover Details';
      case 'scheme':
        return 'Scheme Insurance Details';
      case 'business':
        return 'Business Insurance Details';
      default:
        return 'Quote Details';
    }
  };

  // Render form fields based on quote type
  const renderFormFields = () => {
    if (!currentQuote) return null;

    switch (currentQuote.type) {
      case 'motor':
        return renderMotorForm();
      case 'houseowners':
        return renderHouseownersForm();
      case 'householdContents':
        return renderHouseholdContentsForm();
      case 'allRisks':
        return renderAllRisksForm();
      case 'life':
        return renderLifeForm();
      case 'funeral':
        return renderFuneralForm();
      case 'scheme':
        return renderSchemeForm();
      // Add other quote types as needed
      default:
        return (
          <View style={styles.notImplementedContainer}>
            <Info size={48} color={colors.warning[500]} />
            <Text style={styles.notImplementedText}>
              Form for {currentQuote.type} insurance is not yet implemented.
            </Text>
          </View>
        );
    }
  };

  // Motor insurance form
  const renderMotorForm = () => {
    const usageOptions: SelectOption[] = [
      { value: 'personal', label: 'Personal Use' },
      { value: 'commercial', label: 'Commercial Use' },
    ];

    const coverTypeOptions: SelectOption[] = [
      { value: 'comprehensive', label: 'Comprehensive' },
      { value: 'thirdParty', label: 'Third Party Only' },
      { value: 'thirdPartyFireAndTheft', label: 'Third Party, Fire & Theft' },
    ];

    return (
      <View>
        <DynamicFormField
          label="Vehicle Make"
          type="text"
          value={formData.vehicleMake}
          onChange={(value) => updateFormField('vehicleMake', value)}
          placeholder="e.g., Toyota, Honda, Ford"
          error={errors.vehicleMake}
          required
          icon={<Car size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Vehicle Model"
          type="text"
          value={formData.vehicleModel}
          onChange={(value) => updateFormField('vehicleModel', value)}
          placeholder="e.g., Corolla, Civic, Focus"
          error={errors.vehicleModel}
          required
          icon={<Car size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Vehicle Year"
          type="number"
          value={formData.vehicleYear?.toString()}
          onChange={(value) => updateFormField('vehicleYear', parseInt(value) || new Date().getFullYear())}
          placeholder="e.g., 2020"
          keyboardType="numeric"
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Vehicle Value"
          type="number"
          value={formData.vehicleValue?.toString()}
          onChange={(value) => updateFormField('vehicleValue', parseFloat(value) || 0)}
          placeholder="e.g., 25000"
          error={errors.vehicleValue}
          required
          keyboardType="numeric"
          icon={<PulaIcon size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Registration Number"
          type="text"
          value={formData.registrationNumber}
          onChange={(value) => updateFormField('registrationNumber', value)}
          placeholder="e.g., ABC123"
          error={errors.registrationNumber}
          required
          icon={<Hash size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Vehicle Usage"
          type="select"
          value={formData.usage}
          onChange={(value) => updateFormField('usage', value)}
          options={usageOptions}
          icon={<Car size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Cover Type"
          type="select"
          value={formData.coverType}
          onChange={(value) => updateFormField('coverType', value)}
          options={coverTypeOptions}
          icon={<Info size={20} color={colors.textSecondary} />}
        />
      </View>
    );
  };

  // Houseowners insurance form
  const renderHouseownersForm = () => {
    const occupancyStatusOptions: SelectOption[] = [
      { value: 'owner', label: 'Owner Occupied' },
      { value: 'tenant', label: 'Tenant Occupied' },
      { value: 'vacant', label: 'Vacant' },
    ];

    const coverTypeOptions: SelectOption[] = [
      { value: 'standard', label: 'Standard Cover' },
      { value: 'comprehensive', label: 'Comprehensive Cover' },
    ];

    return (
      <View>
        <DynamicFormField
          label="Property Address"
          type="text"
          value={formData.propertyAddress}
          onChange={(value) => updateFormField('propertyAddress', value)}
          placeholder="Enter property address"
          error={errors.propertyAddress}
          required
          icon={<Home size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Construction Type"
          type="text"
          value={formData.constructionType}
          onChange={(value) => updateFormField('constructionType', value)}
          placeholder="e.g., Brick, Wood, Concrete"
          error={errors.constructionType}
          required
          icon={<Home size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Occupancy Status"
          type="select"
          value={formData.occupancyStatus}
          onChange={(value) => updateFormField('occupancyStatus', value)}
          options={occupancyStatusOptions}
          icon={<Home size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Building Value"
          type="number"
          value={formData.buildingValue?.toString()}
          onChange={(value) => updateFormField('buildingValue', parseFloat(value) || 0)}
          placeholder="e.g., 250000"
          error={errors.buildingValue}
          required
          keyboardType="numeric"
          icon={<PulaIcon size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Year Built"
          type="number"
          value={formData.yearBuilt?.toString()}
          onChange={(value) => updateFormField('yearBuilt', parseInt(value) || 0)}
          placeholder="e.g., 2000"
          keyboardType="numeric"
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Cover Type"
          type="select"
          value={formData.coverType}
          onChange={(value) => updateFormField('coverType', value)}
          options={coverTypeOptions}
          icon={<Info size={20} color={colors.textSecondary} />}
        />
      </View>
    );
  };

  // Household contents insurance form
  const renderHouseholdContentsForm = () => {
    return (
      <View>
        <DynamicFormField
          label="Property Address"
          type="text"
          value={formData.propertyAddress}
          onChange={(value) => updateFormField('propertyAddress', value)}
          placeholder="Enter property address"
          error={errors.propertyAddress}
          required
          icon={<Home size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Contents Value"
          type="number"
          value={formData.contentsValue?.toString()}
          onChange={(value) => updateFormField('contentsValue', parseFloat(value) || 0)}
          placeholder="e.g., 50000"
          error={errors.contentsValue}
          required
          keyboardType="numeric"
          icon={<PulaIcon size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Power Surge Protection"
          type="switch"
          value={formData.powerSurgeProtection}
          onChange={(value) => updateFormField('powerSurgeProtection', value)}
        />

        <DynamicFormField
          label="Accidental Damage"
          type="switch"
          value={formData.accidentalDamage}
          onChange={(value) => updateFormField('accidentalDamage', value)}
        />
      </View>
    );
  };

  // All risks insurance form
  const renderAllRisksForm = () => {
    return (
      <View>
        <DynamicFormField
          label="Unspecified Items Value"
          type="number"
          value={formData.unspecifiedItemsValue?.toString()}
          onChange={(value) => updateFormField('unspecifiedItemsValue', parseFloat(value) || 0)}
          placeholder="e.g., 10000"
          error={errors.unspecifiedItemsValue}
          required
          keyboardType="numeric"
          icon={<PulaIcon size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Specified Items</Text>
        <Text style={styles.sectionDescription}>
          For high-value items that need individual coverage, please add them below.
          You will be asked to upload proof of purchase and photos later.
        </Text>

        {/* In a real app, you would add a component to manage specified items */}
        <View style={styles.notImplementedContainer}>
          <Info size={24} color={colors.warning[500]} />
          <Text style={styles.notImplementedText}>
            Specified items management will be implemented in a future update.
          </Text>
        </View>
      </View>
    );
  };

  // Life insurance form
  const renderLifeForm = () => {
    const genderOptions: SelectOption[] = [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'other', label: 'Other' },
    ];

    return (
      <View>
        <Text style={styles.sectionTitle}>Insured Person Details</Text>

        <DynamicFormField
          label="Date of Birth"
          type="date"
          value={formData.insuredPerson?.dateOfBirth}
          onChange={(value) => updateFormField('insuredPerson', { ...formData.insuredPerson, dateOfBirth: value })}
          placeholder="Select date of birth"
          error={errors.dateOfBirth}
          required
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Gender"
          type="select"
          value={formData.insuredPerson?.gender}
          onChange={(value) => updateFormField('insuredPerson', { ...formData.insuredPerson, gender: value })}
          options={genderOptions}
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Smoker"
          type="switch"
          value={formData.insuredPerson?.smoker}
          onChange={(value) => updateFormField('insuredPerson', { ...formData.insuredPerson, smoker: value })}
        />

        <DynamicFormField
          label="Occupation"
          type="text"
          value={formData.insuredPerson?.occupation}
          onChange={(value) => updateFormField('insuredPerson', { ...formData.insuredPerson, occupation: value })}
          placeholder="Enter occupation"
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Coverage Details</Text>

        <DynamicFormField
          label="Coverage Amount"
          type="number"
          value={formData.coverageAmount?.toString()}
          onChange={(value) => updateFormField('coverageAmount', parseFloat(value) || 0)}
          placeholder="e.g., 100000"
          error={errors.coverageAmount}
          required
          keyboardType="numeric"
          icon={<PulaIcon size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Term (Years)"
          type="number"
          value={formData.term?.toString()}
          onChange={(value) => updateFormField('term', parseInt(value) || 0)}
          placeholder="e.g., 20"
          keyboardType="numeric"
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Beneficiaries</Text>

        {/* In a real app, you would add a component to manage beneficiaries */}
        <View style={styles.notImplementedContainer}>
          <Info size={24} color={colors.warning[500]} />
          <Text style={styles.notImplementedText}>
            Beneficiary management will be implemented in a future update.
          </Text>
        </View>
      </View>
    );
  };

  // Funeral cover form
  const renderFuneralForm = () => {
    const genderOptions: SelectOption[] = [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'other', label: 'Other' },
    ];

    return (
      <View>
        <Text style={styles.sectionTitle}>Insured Person Details</Text>

        <DynamicFormField
          label="Date of Birth"
          type="date"
          value={formData.insuredPerson?.dateOfBirth}
          onChange={(value) => updateFormField('insuredPerson', { ...formData.insuredPerson, dateOfBirth: value })}
          placeholder="Select date of birth"
          error={errors.dateOfBirth}
          required
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Gender"
          type="select"
          value={formData.insuredPerson?.gender}
          onChange={(value) => updateFormField('insuredPerson', { ...formData.insuredPerson, gender: value })}
          options={genderOptions}
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Coverage Details</Text>

        <DynamicFormField
          label="Coverage Amount"
          type="number"
          value={formData.coverageAmount?.toString()}
          onChange={(value) => updateFormField('coverageAmount', parseFloat(value) || 0)}
          placeholder="e.g., 20000"
          error={errors.coverageAmount}
          required
          keyboardType="numeric"
          icon={<PulaIcon size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Family Members</Text>

        {/* In a real app, you would add a component to manage family members */}
        <View style={styles.notImplementedContainer}>
          <Info size={24} color={colors.warning[500]} />
          <Text style={styles.notImplementedText}>
            Family member management will be implemented in a future update.
          </Text>
        </View>

        <Text style={styles.sectionTitle}>Beneficiaries</Text>

        {/* In a real app, you would add a component to manage beneficiaries */}
        <View style={styles.notImplementedContainer}>
          <Info size={24} color={colors.warning[500]} />
          <Text style={styles.notImplementedText}>
            Beneficiary management will be implemented in a future update.
          </Text>
        </View>
      </View>
    );
  };

  // Scheme insurance form
  const renderSchemeForm = () => {
    return (
      <View>
        <Text style={styles.sectionTitle}>Member Details</Text>

        <DynamicFormField
          label="Employee Number"
          type="text"
          value={formData.memberDetails?.employeeNumber}
          onChange={(value) => updateFormField('memberDetails', { ...formData.memberDetails, employeeNumber: value })}
          placeholder="Enter employee number"
          error={errors.employeeNumber}
          required
          icon={<Hash size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Date of Birth"
          type="date"
          value={formData.memberDetails?.dateOfBirth}
          onChange={(value) => updateFormField('memberDetails', { ...formData.memberDetails, dateOfBirth: value })}
          placeholder="Select date of birth"
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Employer Details</Text>

        <DynamicFormField
          label="Employer Name"
          type="text"
          value={formData.employerName}
          onChange={(value) => updateFormField('employerName', value)}
          placeholder="Enter employer name"
          error={errors.employerName}
          required
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Employer Address"
          type="text"
          value={formData.employerAddress}
          onChange={(value) => updateFormField('employerAddress', value)}
          placeholder="Enter employer address"
          icon={<MapPin size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Scheme Type"
          type="text"
          value={formData.schemeType}
          onChange={(value) => updateFormField('schemeType', value)}
          placeholder="Enter scheme type"
          icon={<Info size={20} color={colors.textSecondary} />}
        />
      </View>
    );
  };

  const styles = StyleSheet.create({
    notImplementedContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
      backgroundColor: `${colors.warning[500]}15`,
      borderRadius: 8,
      marginVertical: spacing.lg,
    },
    notImplementedText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      textAlign: 'center',
      marginTop: spacing.md,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginTop: spacing.lg,
      marginBottom: spacing.sm,
    },
    sectionDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.md,
    },
    loadingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    loadingText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginTop: spacing.md,
      textAlign: 'center',
    },
  });

  // Show loading indicator while checking AsyncStorage
  if (isStorageLoading || isQuoteLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary[500]} />
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading quote data...</Text>
      </View>
    );
  }

  return (
    <QuoteFormContainer
      title={getFormTitle()}
      subtitle="Please provide details for your quote"
      steps={steps}
      currentStep={1}
      completedSteps={[0]}
      onBack={handleBack}
      onNext={handleNext}
      onSave={handleSave}
      isLoading={isQuoteLoading}
      nextDisabled={!formValid}
    >
      {renderFormFields()}
    </QuoteFormContainer>
  );
}
