import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';
import { Platform } from 'react-native';
import Toast from 'react-native-toast-message';
import { getMimeType } from './fileUtils';

/**
 * A more robust file sharing module that handles both iOS and Android
 */
export class FileSharing {
  /**
   * Open a file in an external viewer
   * @param fileUri The URI of the file to open
   * @param mimeType Optional MIME type of the file
   */
  static async openFile(fileUri: string, mimeType?: string): Promise<void> {
    try {
      console.log(`[FileSharing] Opening file: ${fileUri}`);

      // Determine MIME type if not provided
      const fileMimeType = mimeType || getMimeType(fileUri);
      console.log(`[FileSharing] MIME type: ${fileMimeType}`);

      // Check if the URI is a remote URL or API endpoint
      const isRemoteUrl = fileUri.startsWith('http://') || fileUri.startsWith('https://') || fileUri.startsWith('/api/');

      // If it's an API endpoint without http, prepend the base URL
      if (fileUri.startsWith('/api/')) {
        // Get the base URL from environment or config
        const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL || 'https://inerca-backend-wild-leaf-8326.fly.dev/';
        fileUri = `${baseUrl}${fileUri}`;
      }

      if (isRemoteUrl) {
        // For remote URLs, download the file first
        console.log(`[FileSharing] Downloading remote file: ${fileUri}`);

        // Show downloading toast
        Toast.show({
          type: 'info',
          text1: 'Downloading',
          text2: 'Preparing file for viewing...',
          visibilityTime: 2000,
          topOffset: 60,
        });

        try {
          // Download the file to a local path
          const localUri = await this.downloadFile(fileUri, fileMimeType);

          // Open the local file
          if (Platform.OS === 'android') {
            await this.openFileAndroid(localUri, fileMimeType);
          } else {
            await this.openFileIOS(localUri, fileMimeType);
          }

          // Show success toast
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Opening file in external viewer',
            visibilityTime: 2000,
            topOffset: 60,
          });
        } catch (downloadError) {
          console.error(`[FileSharing] Error downloading file:`, downloadError);

          // If download fails, try to open the URL directly in browser as fallback
          await WebBrowser.openBrowserAsync(fileUri);

          Toast.show({
            type: 'info',
            text1: 'Opening in Browser',
            text2: 'File is being opened in your browser',
            visibilityTime: 3000,
            topOffset: 60,
          });
        }
      } else {
        // For local files, check if the file exists
        const fileInfo = await FileSystem.getInfoAsync(fileUri);
        if (!fileInfo.exists) {
          throw new Error(`File does not exist: ${fileUri}`);
        }

        // Handle platform-specific file opening
        if (Platform.OS === 'android') {
          await this.openFileAndroid(fileUri, fileMimeType);
        } else {
          await this.openFileIOS(fileUri, fileMimeType);
        }

        // Show success toast
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Opening file in external viewer',
          visibilityTime: 2000,
          topOffset: 60,
        });
      }
    } catch (error) {
      console.error(`[FileSharing] Error opening file:`, error);

      // Show error toast
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to open file. Please try again.',
        visibilityTime: 4000,
        topOffset: 60,
      });

      throw error;
    }
  }

  /**
   * Download a file from a remote URL
   * Public method for direct downloading
   */
  static async downloadFile(url: string, mimeType: string): Promise<string> {
    try {
      // Create a directory for downloaded files
      const downloadDir = `${FileSystem.cacheDirectory}downloads/`;
      const dirInfo = await FileSystem.getInfoAsync(downloadDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(downloadDir, { intermediates: true });
      }

      // Generate a unique filename based on the URL and timestamp
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 8);
      const urlFilename = url.split('/').pop() || 'file';

      // Add appropriate extension if missing
      let filename = `${timestamp}_${randomString}_${urlFilename}`;
      if (!filename.includes('.')) {
        // Add extension based on MIME type
        if (mimeType === 'application/pdf') {
          filename += '.pdf';
        } else if (mimeType.startsWith('image/')) {
          filename += `.${mimeType.split('/')[1]}`;
        } else if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          filename += '.docx';
        } else if (mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          filename += '.xlsx';
        }
      }

      const localUri = `${downloadDir}${filename}`;
      console.log(`[FileSharing] Downloading to: ${localUri}`);

      // Download the file
      const downloadResult = await FileSystem.downloadAsync(url, localUri);

      if (downloadResult.status !== 200) {
        throw new Error(`Download failed with status ${downloadResult.status}`);
      }

      console.log(`[FileSharing] Download complete: ${localUri}`);
      return localUri;
    } catch (error) {
      console.error(`[FileSharing] Error downloading file:`, error);
      throw new Error('Download failed');
    }
  }

  /**
   * Open a file on Android
   * @private
   */
  private static async openFileAndroid(fileUri: string, mimeType: string): Promise<void> {
    try {
      // Create a temporary directory for sharing
      const tempDir = `${FileSystem.cacheDirectory}temp_files/`;
      const dirInfo = await FileSystem.getInfoAsync(tempDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(tempDir, { intermediates: true });
      }

      // Generate a unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 8);
      const originalFilename = fileUri.split('/').pop() || 'file';
      const filename = `${timestamp}_${randomString}_${originalFilename}`;
      const tempFilePath = `${tempDir}${filename}`;

      console.log(`[FileSharing] Copying file to: ${tempFilePath}`);

      // Copy the file to the temp directory
      await FileSystem.copyAsync({
        from: fileUri,
        to: tempFilePath
      });

      // Use FileSystem.getContentUriAsync to get a content:// URI
      // This is the key fix for the FileUriExposedException
      const contentUri = await FileSystem.getContentUriAsync(tempFilePath);
      console.log(`[FileSharing] Content URI: ${contentUri}`);

      // Use IntentLauncher with the content URI
      console.log(`[FileSharing] Opening with IntentLauncher`);
      await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
        data: contentUri,
        flags: 1,
        type: mimeType
      });
    } catch (error) {
      console.error(`[FileSharing] Error in openFileAndroid:`, error);

      // Fallback to direct sharing
      console.log(`[FileSharing] Falling back to direct sharing`);
      try {
        await Sharing.shareAsync(fileUri, {
          mimeType: mimeType,
          dialogTitle: 'View File'
        });
      } catch (sharingError) {
        console.error(`[FileSharing] Error in sharing fallback:`, sharingError);
        throw new Error('Failed to open file with any available method');
      }
    }
  }

  /**
   * Open a file on iOS
   * @private
   */
  private static async openFileIOS(fileUri: string, mimeType: string): Promise<void> {
    // Get the appropriate UTI for the MIME type
    const uti = this.getUTIForMimeType(mimeType);
    console.log(`[FileSharing] iOS UTI: ${uti}`);

    // Use Sharing API to open the file
    await Sharing.shareAsync(fileUri, {
      UTI: uti,
      mimeType: mimeType,
      dialogTitle: 'View File'
    });
  }

  /**
   * Get UTI for MIME type (iOS only)
   * @private
   */
  private static getUTIForMimeType(mimeType: string): string {
    switch (mimeType) {
      case 'application/pdf':
        return 'com.adobe.pdf';
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return 'org.openxmlformats.wordprocessingml.document';
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return 'org.openxmlformats.spreadsheetml.sheet';
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return 'org.openxmlformats.presentationml.presentation';
      case 'text/plain':
        return 'public.plain-text';
      case 'text/rtf':
        return 'public.rtf';
      case 'image/jpeg':
        return 'public.jpeg';
      case 'image/png':
        return 'public.png';
      case 'image/gif':
        return 'public.gif';
      default:
        return 'public.data';
    }
  }
}
