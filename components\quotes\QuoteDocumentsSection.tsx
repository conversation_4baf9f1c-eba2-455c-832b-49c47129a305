import React, { useState, useEffect, memo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { FileText, Upload, Check, AlertCircle, X, Clock } from 'lucide-react-native';
import { QuoteDocument, InsuranceProductType } from '@/types/quote.types';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Document, DocumentCategory } from '@/components/documents/types';
import { useVerifiedDocuments } from '@/context/OptimizedUploadContext';
import { showToast } from '@/utils/toast';
import UltimateDocumentUploader from '@/components/documents/UltimateDocumentUploader';

interface QuoteDocumentsSectionProps {
  quoteType: InsuranceProductType;
  documents: QuoteDocument[];
  onDocumentsUpdated: (documents: QuoteDocument[]) => void;
}

const QuoteDocumentsSection: React.FC<QuoteDocumentsSectionProps> = ({
  quoteType,
  documents,
  onDocumentsUpdated,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { documents: verifiedDocuments, addDocument } = useVerifiedDocuments();

  // Helper functions for compatibility
  const getVerifiedDocumentByType = (documentType: string) => {
    return verifiedDocuments.find(doc => doc.name === documentType) || null;
  };

  const documentsInVerification: string[] = [];
  const getDocumentById = (id: string) => verifiedDocuments.find(doc => doc.id === id) || null;
  const pendingDocuments: Document[] = [];

  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState<string | null>(null);

  // Get required documents based on quote type
  const getRequiredDocuments = (type: InsuranceProductType): QuoteDocument[] => {
    // Common documents for all quote types
    const commonDocs: QuoteDocument[] = [
      {
        id: 'common-1',
        name: 'National ID/Omang/Passport',
        type: 'National ID/Omang/Passport',
        category: 'ID',
        required: true,
        uploaded: false,
      },
      {
        id: 'common-2',
        name: 'Proof of Address',
        type: 'Proof of Address',
        category: 'Proof of Address',
        required: true,
        uploaded: false,
      },
    ];

    // Type-specific documents
    let typeDocs: QuoteDocument[] = [];

    switch (type) {
      case 'motor':
        typeDocs = [
          {
            id: 'motor-1',
            name: 'Driver\'s License',
            type: 'Driver\'s License',
            category: 'ID',
            required: true,
            uploaded: false,
          },
          {
            id: 'motor-2',
            name: 'Vehicle Registration Book',
            type: 'Vehicle Registration Book',
            category: 'ID',
            required: true,
            uploaded: false,
          },
          {
            id: 'motor-3',
            name: 'Vehicle Valuation Report',
            type: 'Vehicle Valuation Report',
            category: 'Insurance',
            required: false,
            uploaded: false,
          },
          {
            id: 'motor-4',
            name: 'Claim-Free Group Letter',
            type: 'Claim-Free Group Letter',
            category: 'Insurance',
            required: false,
            uploaded: false,
          },
        ];
        break;
      case 'houseowners':
        typeDocs = [
          {
            id: 'house-1',
            name: 'Title Deed/Land Board Certificate',
            type: 'Title Deed/Land Board Certificate',
            category: 'ID',
            required: true,
            uploaded: false,
          },
          {
            id: 'house-2',
            name: 'Building Valuation Report',
            type: 'Building Valuation Report',
            category: 'Insurance',
            required: true,
            uploaded: false,
          },
        ];
        break;
      case 'householdContents':
        typeDocs = [
          {
            id: 'contents-1',
            name: 'Completed Inventory Form',
            type: 'Completed Inventory Form',
            category: 'Insurance',
            required: true,
            uploaded: false,
          },
        ];
        break;
      case 'life':
        typeDocs = [
          {
            id: 'life-1',
            name: 'Beneficiary Nomination Form',
            type: 'Beneficiary Nomination Form',
            category: 'Insurance',
            required: true,
            uploaded: false,
          },
          {
            id: 'life-2',
            name: 'Medical Questionnaire',
            type: 'Medical Questionnaire',
            category: 'Insurance',
            required: true,
            uploaded: false,
          },
          {
            id: 'life-3',
            name: 'Income Proof',
            type: 'Income Proof',
            category: 'Financial',
            required: true,
            uploaded: false,
          },
          {
            id: 'life-4',
            name: 'Medical Examination Report',
            type: 'Medical Examination Report',
            category: 'Insurance',
            required: false,
            uploaded: false,
          },
          {
            id: 'life-5',
            name: 'Blood Test Results',
            type: 'Blood Test Results',
            category: 'Medical',
            required: false,
            uploaded: false,
          },
          {
            id: 'life-6',
            name: 'Employment Certificate',
            type: 'Employment Certificate',
            category: 'Financial',
            required: true,
            uploaded: false,
          },
          {
            id: 'life-7',
            name: 'Lifestyle Questionnaire',
            type: 'Lifestyle Questionnaire',
            category: 'Insurance',
            required: true,
            uploaded: false,
          },
          {
            id: 'life-8',
            name: 'Family Medical History',
            type: 'Family Medical History',
            category: 'Medical',
            required: false,
            uploaded: false,
          },
        ];
        break;
      default:
        // No additional documents for other types
        break;
    }

    return [...commonDocs, ...typeDocs];
  };

  // Initialize documents if empty
  React.useEffect(() => {
    if (documents.length === 0) {
      onDocumentsUpdated(getRequiredDocuments(quoteType));
    }
  }, [quoteType]);

  // Handle document upload - memoized to prevent unnecessary re-renders
  const handleDocumentUpload = React.useCallback((documentType: string) => {
    // Check if there's already a verified document of this type
    const verifiedDocument = getVerifiedDocumentByType(documentType);

    if (verifiedDocument) {
      // If a verified document exists, use it instead of asking for a new upload
      console.log('Using existing verified document:', verifiedDocument.id);

      // Find the document in the list
      const docIndex = documents.findIndex(doc => doc.type === documentType);

      if (docIndex !== -1) {
        // Update the document status
        const updatedDocs = [...documents];
        updatedDocs[docIndex] = {
          ...updatedDocs[docIndex],
          uploaded: true,
          documentId: verifiedDocument.id,
          status: 'verified',
          uploadDate: new Date().toISOString().split('T')[0],
        };

        onDocumentsUpdated(updatedDocs);

        // Show success message
        showToast(
          'success',
          'Document Reused',
          `${updatedDocs[docIndex].name} has been automatically added from your verified documents`,
          { visibilityTime: 3000 }
        );
      }
    } else {
      // Check if there's a pending document of this type
      const pendingDoc = pendingDocuments.find(doc => doc.name === documentType);

      if (pendingDoc) {
        // If a pending document exists, use it
        console.log('Using existing pending document:', pendingDoc.id);

        // Find the document in the list
        const docIndex = documents.findIndex(doc => doc.type === documentType);

        if (docIndex !== -1) {
          // Update the document status
          const updatedDocs = [...documents];
          updatedDocs[docIndex] = {
            ...updatedDocs[docIndex],
            uploaded: true,
            documentId: pendingDoc.id,
            status: pendingDoc.status,
            uploadDate: new Date().toISOString().split('T')[0],
            reason: pendingDoc.reason,
          };

          onDocumentsUpdated(updatedDocs);

          // Show appropriate message based on document status
          if (pendingDoc.status === 'pending') {
            showToast(
              'info',
              'Document Pending',
              `${updatedDocs[docIndex].name} is pending verification`,
              { visibilityTime: 3000 }
            );
          } else if (pendingDoc.status === 'rejected') {
            showToast(
              'error',
              'Document Rejected',
              `${updatedDocs[docIndex].name} was rejected: ${pendingDoc.reason || 'Unknown reason'}`,
              { visibilityTime: 3000 }
            );
          }
        }
      } else {
        // If no document exists, show the upload modal
        setSelectedDocumentType(documentType);
        setUploadModalVisible(true);
      }
    }
  }, [getVerifiedDocumentByType, pendingDocuments, documents, onDocumentsUpdated, setSelectedDocumentType, setUploadModalVisible]);

  // Handle document uploaded - memoized to prevent unnecessary re-renders
  const handleDocumentUploaded = React.useCallback((document: Document) => {
    // Add document to verified documents
    addDocument(document);
    // Find the document in the list
    const docIndex = documents.findIndex(doc => doc.type === selectedDocumentType);

    if (docIndex !== -1) {
      // Update the document status
      const updatedDocs = [...documents];
      updatedDocs[docIndex] = {
        ...updatedDocs[docIndex],
        uploaded: true,
        documentId: document.id,
        status: document.status,
        uploadDate: new Date().toISOString().split('T')[0],
        reason: document.reason,
      };

      onDocumentsUpdated(updatedDocs);
      setUploadModalVisible(false);

      // Show appropriate message based on document status
      if (document.status === 'pending') {
        showToast(
          'success',
          'Document Uploaded',
          `${updatedDocs[docIndex].name} has been uploaded and is pending verification`,
          { visibilityTime: 3000 }
        );
      } else if (document.status === 'verified') {
        showToast(
          'success',
          'Document Verified',
          `${updatedDocs[docIndex].name} has been verified`,
          { visibilityTime: 3000 }
        );
      } else if (document.status === 'rejected') {
        showToast(
          'error',
          'Document Rejected',
          `${updatedDocs[docIndex].name} was rejected: ${document.reason || 'Unknown reason'}`,
          { visibilityTime: 3000 }
        );
      }
    }
  }, [documents, selectedDocumentType, onDocumentsUpdated, addDocument]);

  // Create a ref at the top level of the component
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Update document status when verification status changes - with debounce
  useEffect(() => {
    console.log('[QuoteDocumentsSection] Checking for document status updates');

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set a new timeout to debounce the updates
    timeoutRef.current = setTimeout(() => {
      // Check if any documents need status updates
      const updatedDocs = [...documents];
      let hasChanges = false;

      documents.forEach((doc, index) => {
        if (doc.uploaded && doc.documentId) {
          // Get the current document from the context
          const currentDoc = getDocumentById(doc.documentId);
          console.log(`[QuoteDocumentsSection] Checking document ${doc.documentId}, status: ${doc.status}, context status: ${currentDoc?.status}`);

          if (currentDoc) {
            // Check if status has changed
            if (currentDoc.status !== doc.status) {
              console.log(`[QuoteDocumentsSection] Status changed for document ${doc.documentId}: ${doc.status} -> ${currentDoc.status}`);

              updatedDocs[index] = {
                ...updatedDocs[index],
                status: currentDoc.status,
                reason: currentDoc.reason, // Also update the reason
              };
              hasChanges = true;

              // Show toast notification for status change only if not pending
              if (currentDoc.status === 'verified') {
                showToast(
                  'success',
                  'Document Verified',
                  `${doc.name} has been verified`,
                  { visibilityTime: 3000 }
                );
              } else if (currentDoc.status === 'rejected') {
                showToast(
                  'error',
                  'Document Rejected',
                  `${doc.name} was rejected: ${currentDoc.reason || 'Unknown reason'}`,
                  { visibilityTime: 3000 }
                );
              }
            }
          }

          // Check if document is in verification process
          const isInVerification = documentsInVerification.includes(doc.documentId);
          if (isInVerification && doc.status !== 'pending') {
            console.log(`[QuoteDocumentsSection] Document ${doc.documentId} is in verification but status is ${doc.status}, updating to pending`);

            updatedDocs[index] = {
              ...updatedDocs[index],
              status: 'pending',
            };
            hasChanges = true;
          }
        }
      });

      // Update documents if there are changes
      if (hasChanges) {
        console.log('[QuoteDocumentsSection] Updating documents with new status information');
        onDocumentsUpdated(updatedDocs);
      }
    }, 1000); // 1000ms debounce time - increased to ensure we catch all updates

    // Set up a polling interval to check for document status changes
    const pollingInterval = setInterval(() => {
      console.log('[QuoteDocumentsSection] Polling for document status updates');

      // Force the effect to run again by clearing and setting the timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        // Check if any documents need status updates
        const updatedDocs = [...documents];
        let hasChanges = false;

        documents.forEach((doc, index) => {
          if (doc.uploaded && doc.documentId) {
            // Get the current document from the context
            const currentDoc = getDocumentById(doc.documentId);

            if (currentDoc) {
              // Check if status has changed
              if (currentDoc.status !== doc.status) {
                console.log(`[QuoteDocumentsSection] Status changed for document ${doc.documentId}: ${doc.status} -> ${currentDoc.status}`);

                updatedDocs[index] = {
                  ...updatedDocs[index],
                  status: currentDoc.status,
                  reason: currentDoc.reason,
                };
                hasChanges = true;

                // Show toast notification for status change only if not pending
                if (currentDoc.status === 'verified') {
                  showToast(
                    'success',
                    'Document Verified',
                    `${doc.name} has been verified`,
                    { visibilityTime: 3000 }
                  );
                } else if (currentDoc.status === 'rejected') {
                  showToast(
                    'error',
                    'Document Rejected',
                    `${doc.name} was rejected: ${currentDoc.reason || 'Unknown reason'}`,
                    { visibilityTime: 3000 }
                  );
                }
              }
            }
          }
        });

        // Update documents if there are changes
        if (hasChanges) {
          console.log('[QuoteDocumentsSection] Updating documents with new status information from polling');
          onDocumentsUpdated(updatedDocs);
        }
      }, 100);
    }, 5000); // Check every 5 seconds

    // Clean up the timeout and interval on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      clearInterval(pollingInterval);
    };
  }, [documents, documentsInVerification, getDocumentById, onDocumentsUpdated, showToast]);

  // Check if all required documents are uploaded - memoized to prevent unnecessary re-renders
  const areAllRequiredDocumentsUploaded = React.useCallback(() => {
    return documents.every(doc => !doc.required || doc.uploaded);
  }, [documents]);

  // Styles - memoized to prevent unnecessary re-renders
  const styles = React.useMemo(() => StyleSheet.create({
    container: {
      marginBottom: spacing.lg,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    documentItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.sm,
      borderLeftWidth: 3,
    },
    documentIcon: {
      marginRight: spacing.md,
      flexShrink: 0, // Prevent icon from shrinking
    },
    documentContent: {
      flex: 1,
      minWidth: 0, // Important for text truncation to work
    },
    documentName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: 2,
      flexShrink: 1, // Allow text to shrink
      flexWrap: 'wrap', // Allow text to wrap
    },
    documentStatus: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary[50],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.md,
      flexShrink: 0, // Prevent button from shrinking
      marginLeft: spacing.xs, // Add some space between content and button
    },
    actionButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.primary[500],
      marginLeft: spacing.xs,
    },
    requiredBadge: {
      backgroundColor: colors.error[50],
      paddingHorizontal: spacing.xs,
      paddingVertical: 2,
      borderRadius: borders.radius.sm,
      marginLeft: spacing.xs,
    },
    requiredText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xxs,
      color: colors.error[500],
    },
    statusBadge: {
      paddingHorizontal: spacing.xs,
      paddingVertical: 2,
      borderRadius: borders.radius.sm,
      marginLeft: spacing.xs,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xxs,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    closeButton: {
      padding: spacing.xs,
    },
    modalContent: {
      flex: 1,
      padding: spacing.md,
    },
    warningContainer: {
      backgroundColor: colors.warning[50],
      padding: spacing.md,
      borderRadius: borders.radius.md,
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    warningText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.warning[700],
      flex: 1,
      marginLeft: spacing.sm,
    },
  }), [colors, spacing, typography, borders]);

  // Render document item - memoized to prevent unnecessary re-renders
  const renderDocumentItem = React.useCallback((item: QuoteDocument, index: number) => {
    const isUploaded = item.uploaded;
    const isInVerification = item.documentId ? documentsInVerification.includes(item.documentId) : false;

    // Get the current document from the context if it exists
    const currentDoc = item.documentId ? getDocumentById(item.documentId) : null;

    // Use the most up-to-date status
    const documentStatus = currentDoc ? currentDoc.status : item.status;
    const documentReason = currentDoc ? currentDoc.reason : item.reason;

    const borderColor = item.required
      ? isUploaded
        ? documentStatus === 'verified'
          ? colors.success[500]
          : documentStatus === 'rejected'
            ? colors.error[500]
            : isInVerification
              ? colors.warning[500]
              : colors.warning[500]
        : colors.error[500]
      : isUploaded
        ? documentStatus === 'verified'
          ? colors.success[500]
          : documentStatus === 'rejected'
            ? colors.error[500]
            : isInVerification
              ? colors.warning[500]
              : colors.warning[500]
        : colors.warning[500];

    let statusColor = colors.textSecondary;
    let statusText = 'Not uploaded';
    let statusBgColor = 'transparent';
    let statusIcon = null;

    if (isUploaded) {
      switch (documentStatus) {
        case 'verified':
          statusColor = colors.success[500];
          statusText = 'Verified';
          statusBgColor = colors.success[50];
          statusIcon = <Check size={12} color={statusColor} />;
          break;
        case 'rejected':
          statusColor = colors.error[500];
          statusText = documentReason ? `Rejected: ${documentReason}` : 'Rejected';
          statusBgColor = colors.error[50];
          statusIcon = <AlertCircle size={12} color={statusColor} />;
          break;
        default:
          statusColor = colors.warning[500];
          statusText = 'Pending verification';
          statusBgColor = colors.warning[50];
          statusIcon = <Clock size={12} color={statusColor} />;
          break;
      }
    }

    return (
      <Animated.View
        key={item.id}
        entering={FadeInDown.delay(100 + index * 50).springify()}
      >
        <View style={[styles.documentItem, { borderLeftColor: borderColor }]}>
          <FileText
            size={24}
            color={isUploaded ? statusColor : colors.textSecondary}
            style={styles.documentIcon}
          />

          <View style={styles.documentContent}>
            <View style={{ flexDirection: 'row', alignItems: 'flex-start', flexWrap: 'wrap' }}>
              <Text style={styles.documentName} numberOfLines={2} ellipsizeMode="tail">
                {item.name}
              </Text>
              {item.required && (
                <View style={styles.requiredBadge}>
                  <Text style={styles.requiredText}>REQUIRED</Text>
                </View>
              )}
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {statusIcon && <View style={{ marginRight: 4 }}>{statusIcon}</View>}
              <Text style={[styles.documentStatus, isUploaded && { color: statusColor }]}>
                {statusText}
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.actionButton,
              {
                backgroundColor: isInVerification
                  ? `${colors.textSecondary}15`
                  : documentStatus === 'rejected'
                    ? `${colors.error[500]}15`
                    : `${colors.primary[500]}15`
              }
            ]}
            onPress={() => handleDocumentUpload(item.type)}
            disabled={isInVerification}
          >
            <Upload
              size={16}
              color={
                isInVerification
                  ? colors.textSecondary
                  : documentStatus === 'rejected'
                    ? colors.error[500]
                    : colors.primary[500]
              }
            />
            <Text
              style={[
                styles.actionButtonText,
                {
                  color: isInVerification
                    ? colors.textSecondary
                    : documentStatus === 'rejected'
                      ? colors.error[500]
                      : colors.primary[500]
                }
              ]}
            >
              {isInVerification
                ? 'Verifying...'
                : isUploaded
                  ? documentStatus === 'rejected'
                    ? 'Re-upload'
                    : 'Replace'
                  : 'Upload'
              }
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  }, [colors, styles, documentsInVerification, getDocumentById, handleDocumentUpload]);

  // Memoize the document items to prevent unnecessary re-renders
  const documentItems = React.useMemo(() =>
    documents.map((doc, index) => renderDocumentItem(doc, index)),
    [documents, renderDocumentItem]
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Required Documents</Text>

      {!areAllRequiredDocumentsUploaded() && (
        <View style={styles.warningContainer}>
          <AlertCircle size={20} color={colors.warning[700]} />
          <Text style={styles.warningText}>
            Please upload all required documents to proceed with your quote submission.
          </Text>
        </View>
      )}

      {documentItems}

      {/* Document Upload Modal */}
      <Modal
        visible={uploadModalVisible}
        animationType="slide"
        onRequestClose={() => setUploadModalVisible(false)}
        // Add hardwareAccelerated for better performance on Android
        hardwareAccelerated={true}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Upload Document</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setUploadModalVisible(false)}
            >
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.modalContent}
            // Add these props to improve scrolling performance
            removeClippedSubviews={true}
            showsVerticalScrollIndicator={false}
          >
            {/* Use our UltimateDocumentUploader with comprehensive error handling */}
            {uploadModalVisible && (
              <UltimateDocumentUploader
                onDocumentUploaded={handleDocumentUploaded}
                preselectedDocumentType={selectedDocumentType || undefined}
                maxFileSize={10 * 1024 * 1024}
                allowedFileTypes={['image', 'pdf', 'doc']}
                uploadType="document"
              />
            )}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

export default QuoteDocumentsSection;
