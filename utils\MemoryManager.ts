/**
 * MemoryManager.ts
 * 
 * Comprehensive memory management utility for upload operations
 * Prevents memory leaks and manages resource cleanup
 */

import { AppState, AppStateStatus } from 'react-native';
import { UploadManager } from './UploadManager';

interface MemoryStats {
  timestamp: number;
  activeUploads: number;
  queueLength: number;
  memoryWarnings: number;
  lastCleanup: number;
}

interface ResourceTracker {
  id: string;
  type: 'upload' | 'image' | 'document' | 'callback' | 'timeout';
  createdAt: number;
  size?: number;
  cleanup?: () => void;
}

class MemoryManagerClass {
  private resources: Map<string, ResourceTracker> = new Map();
  private memoryStats: MemoryStats[] = [];
  private cleanupInterval: NodeJS.Timeout | null = null;
  private memoryCheckInterval: NodeJS.Timeout | null = null;
  private appStateSubscription: any = null;
  private isLowMemoryMode: boolean = false;
  private memoryWarningCount: number = 0;
  
  // Configuration
  private readonly CLEANUP_INTERVAL = 2 * 60 * 1000; // 2 minutes
  private readonly MEMORY_CHECK_INTERVAL = 30 * 1000; // 30 seconds
  private readonly MAX_RESOURCE_AGE = 10 * 60 * 1000; // 10 minutes
  private readonly MAX_MEMORY_STATS = 50; // Keep last 50 memory snapshots
  private readonly MEMORY_WARNING_THRESHOLD = 5; // Trigger cleanup after 5 warnings

  constructor() {
    this.initialize();
  }

  private initialize() {
    console.log('[MemoryManager] Initializing memory management');
    
    // Start cleanup intervals
    this.startCleanupInterval();
    this.startMemoryMonitoring();
    
    // Listen for app state changes
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange.bind(this));
    
    // Listen for memory warnings (if available)
    this.setupMemoryWarningListener();
  }

  private startCleanupInterval() {
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.CLEANUP_INTERVAL);
  }

  private startMemoryMonitoring() {
    this.memoryCheckInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, this.MEMORY_CHECK_INTERVAL);
  }

  private handleAppStateChange(nextAppState: AppStateStatus) {
    if (nextAppState === 'background') {
      console.log('[MemoryManager] App backgrounded - performing aggressive cleanup');
      this.performAggressiveCleanup();
    } else if (nextAppState === 'active') {
      console.log('[MemoryManager] App foregrounded - resuming normal operation');
      this.isLowMemoryMode = false;
    }
  }

  private setupMemoryWarningListener() {
    // Note: React Native doesn't have a direct memory warning API
    // This is a placeholder for potential future implementation
    // In a real app, you might use native modules to detect memory pressure
  }

  private checkMemoryUsage() {
    const stats: MemoryStats = {
      timestamp: Date.now(),
      activeUploads: UploadManager.queueStatus.activeUploads,
      queueLength: UploadManager.queueStatus.queueLength,
      memoryWarnings: this.memoryWarningCount,
      lastCleanup: this.getLastCleanupTime(),
    };

    this.memoryStats.push(stats);
    
    // Keep only recent stats
    if (this.memoryStats.length > this.MAX_MEMORY_STATS) {
      this.memoryStats = this.memoryStats.slice(-this.MAX_MEMORY_STATS);
    }

    // Check for memory pressure indicators
    this.detectMemoryPressure(stats);
  }

  private detectMemoryPressure(stats: MemoryStats) {
    const recentStats = this.memoryStats.slice(-5); // Last 5 checks
    
    // Detect if uploads are backing up
    const avgQueueLength = recentStats.reduce((sum, s) => sum + s.queueLength, 0) / recentStats.length;
    const avgActiveUploads = recentStats.reduce((sum, s) => sum + s.activeUploads, 0) / recentStats.length;
    
    if (avgQueueLength > 3 || avgActiveUploads > 2) {
      console.warn('[MemoryManager] Potential memory pressure detected');
      this.memoryWarningCount++;
      
      if (this.memoryWarningCount >= this.MEMORY_WARNING_THRESHOLD) {
        this.enterLowMemoryMode();
      }
    }
  }

  private enterLowMemoryMode() {
    if (this.isLowMemoryMode) return;
    
    console.warn('[MemoryManager] Entering low memory mode');
    this.isLowMemoryMode = true;
    
    // Perform aggressive cleanup
    this.performAggressiveCleanup();
    
    // Increase cleanup frequency
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.CLEANUP_INTERVAL / 2); // Double the frequency
  }

  private getLastCleanupTime(): number {
    const lastStat = this.memoryStats[this.memoryStats.length - 2];
    return lastStat ? lastStat.timestamp : 0;
  }

  // Resource tracking methods
  public trackResource(
    id: string,
    type: ResourceTracker['type'],
    cleanup?: () => void,
    size?: number
  ): void {
    const resource: ResourceTracker = {
      id,
      type,
      createdAt: Date.now(),
      cleanup,
      size,
    };

    this.resources.set(id, resource);
    console.log(`[MemoryManager] Tracking resource: ${id} (${type})`);
  }

  public untrackResource(id: string): void {
    const resource = this.resources.get(id);
    if (resource) {
      // Call cleanup function if provided
      if (resource.cleanup) {
        try {
          resource.cleanup();
        } catch (error) {
          console.error(`[MemoryManager] Error in cleanup for ${id}:`, error);
        }
      }
      
      this.resources.delete(id);
      console.log(`[MemoryManager] Untracked resource: ${id}`);
    }
  }

  // Cleanup methods
  private performCleanup() {
    console.log('[MemoryManager] Performing routine cleanup');
    
    const now = Date.now();
    const expiredResources: string[] = [];
    
    // Find expired resources
    for (const [id, resource] of this.resources.entries()) {
      const age = now - resource.createdAt;
      if (age > this.MAX_RESOURCE_AGE) {
        expiredResources.push(id);
      }
    }
    
    // Clean up expired resources
    expiredResources.forEach(id => {
      console.log(`[MemoryManager] Cleaning up expired resource: ${id}`);
      this.untrackResource(id);
    });
    
    // Clean up upload manager
    UploadManager.clearCompletedUploads();
    
    // Reset memory warning count if cleanup was successful
    if (expiredResources.length > 0) {
      this.memoryWarningCount = Math.max(0, this.memoryWarningCount - 1);
    }
    
    console.log(`[MemoryManager] Cleanup complete. Removed ${expiredResources.length} expired resources`);
  }

  private performAggressiveCleanup() {
    console.log('[MemoryManager] Performing aggressive cleanup');
    
    // Clear all non-essential resources
    const nonEssentialTypes = ['callback', 'timeout'];
    const toRemove: string[] = [];
    
    for (const [id, resource] of this.resources.entries()) {
      if (nonEssentialTypes.includes(resource.type)) {
        toRemove.push(id);
      }
    }
    
    toRemove.forEach(id => this.untrackResource(id));
    
    // Force upload manager cleanup
    UploadManager.clearCompletedUploads();
    
    // Clear old memory stats
    this.memoryStats = this.memoryStats.slice(-10); // Keep only last 10
    
    console.log(`[MemoryManager] Aggressive cleanup complete. Removed ${toRemove.length} resources`);
  }

  // Public API methods
  public getMemoryStats(): MemoryStats[] {
    return [...this.memoryStats];
  }

  public getResourceCount(): number {
    return this.resources.size;
  }

  public getResourcesByType(type: ResourceTracker['type']): ResourceTracker[] {
    const resources: ResourceTracker[] = [];
    for (const resource of this.resources.values()) {
      if (resource.type === type) {
        resources.push({ ...resource });
      }
    }
    return resources;
  }

  public isInLowMemoryMode(): boolean {
    return this.isLowMemoryMode;
  }

  public forceCleanup(): void {
    console.log('[MemoryManager] Force cleanup requested');
    this.performAggressiveCleanup();
  }

  public getMemoryReport(): {
    totalResources: number;
    resourcesByType: Record<string, number>;
    oldestResource: number;
    memoryWarnings: number;
    isLowMemoryMode: boolean;
    lastCleanup: number;
  } {
    const resourcesByType: Record<string, number> = {};
    let oldestResource = Date.now();
    
    for (const resource of this.resources.values()) {
      resourcesByType[resource.type] = (resourcesByType[resource.type] || 0) + 1;
      oldestResource = Math.min(oldestResource, resource.createdAt);
    }
    
    return {
      totalResources: this.resources.size,
      resourcesByType,
      oldestResource: Date.now() - oldestResource,
      memoryWarnings: this.memoryWarningCount,
      isLowMemoryMode: this.isLowMemoryMode,
      lastCleanup: this.getLastCleanupTime(),
    };
  }

  // Cleanup and destroy
  public destroy(): void {
    console.log('[MemoryManager] Destroying memory manager');
    
    // Clear all intervals
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
    }
    
    // Remove app state listener
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
    
    // Clean up all tracked resources
    for (const id of this.resources.keys()) {
      this.untrackResource(id);
    }
    
    // Clear data
    this.resources.clear();
    this.memoryStats = [];
    this.memoryWarningCount = 0;
    this.isLowMemoryMode = false;
  }
}

// Export singleton instance
export const MemoryManager = new MemoryManagerClass();
