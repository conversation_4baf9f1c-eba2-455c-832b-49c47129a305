import React, { memo, useState, useCallback, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Document, DocumentCategory } from './types';
import { UploadProgress } from '@/utils/UploadManager';
import { MemoryManager } from '@/utils/MemoryManager';
import { useUploadOperations, useUploadProgress } from '@/context/OptimizedUploadContext';
import OptimizedDocumentUploader from './OptimizedDocumentUploader';
import UploadErrorBoundary from './UploadErrorBoundary';
import ErrorBoundary from '../common/ErrorBoundary';
import Toast from 'react-native-toast-message';

interface UltimateDocumentUploaderProps {
  onDocumentUploaded: (document: Document) => void;
  preselectedDocumentType?: string;
  disabled?: boolean;
  maxFileSize?: number;
  allowedFileTypes?: string[];
  uploadType?: 'camera' | 'gallery' | 'document';
}

/**
 * Ultimate Document Uploader - The final solution for upload issues
 * 
 * Features:
 * - Queue-based upload system (prevents app rerenders)
 * - Comprehensive error boundaries (prevents crashes)
 * - Memory management (prevents memory leaks)
 * - Extensive React.memo usage (prevents unnecessary rerenders)
 * - Optimized context (minimal reactive state)
 */
const UltimateDocumentUploader: React.FC<UltimateDocumentUploaderProps> = memo(({
  onDocumentUploaded,
  preselectedDocumentType,
  disabled = false,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  allowedFileTypes = ['image', 'pdf', 'doc'],
  uploadType = 'document'
}) => {
  const { isDarkMode } = useTheme();
  const theme = React.useMemo(() => createTheme(isDarkMode), [isDarkMode]);
  const { colors, spacing } = theme;

  // Get upload operations (non-reactive)
  const { uploadImage, uploadDocument, cancelUpload, isLowMemoryMode } = useUploadOperations();
  const { getProgress, subscribe } = useUploadProgress();

  // Local state for current upload
  const [currentUploadId, setCurrentUploadId] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Track component for memory management
  useEffect(() => {
    const componentId = `uploader_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    MemoryManager.trackResource(
      componentId,
      'upload',
      () => {
        // Cleanup on unmount
        if (currentUploadId) {
          cancelUpload(currentUploadId);
        }
      }
    );

    return () => {
      MemoryManager.untrackResource(componentId);
    };
  }, [currentUploadId, cancelUpload]);

  // Handle upload completion
  const handleUploadComplete = useCallback((document: Document) => {
    console.log('[UltimateDocumentUploader] Upload completed:', document.id);
    
    // Reset local state
    setCurrentUploadId(null);
    setUploadProgress(null);
    setRetryCount(0);
    
    // Call parent callback
    onDocumentUploaded(document);
    
    // Show success message
    Toast.show({
      type: 'success',
      text1: 'Upload Successful',
      text2: `${document.name} has been uploaded successfully`,
      visibilityTime: 3000,
    });
  }, [onDocumentUploaded]);

  // Handle upload error
  const handleUploadError = useCallback((error: Error) => {
    console.error('[UltimateDocumentUploader] Upload error:', error);
    
    // Reset local state
    setCurrentUploadId(null);
    setUploadProgress(null);
    
    // Show error message
    Toast.show({
      type: 'error',
      text1: 'Upload Failed',
      text2: error.message,
      visibilityTime: 5000,
    });
  }, []);

  // Start upload with progress tracking
  const startUpload = useCallback(async (
    type: 'image' | 'document',
    useCamera: boolean,
    documentType: string,
    documentCategory: DocumentCategory
  ) => {
    if (currentUploadId || disabled) {
      return;
    }

    // Check memory status
    if (isLowMemoryMode()) {
      Toast.show({
        type: 'warning',
        text1: 'Low Memory',
        text2: 'Device is low on memory. Please close other apps and try again.',
        visibilityTime: 5000,
      });
      return;
    }

    try {
      let uploadId: string;
      
      if (type === 'image') {
        uploadId = await uploadImage(useCamera, documentType, documentCategory);
      } else {
        uploadId = await uploadDocument(documentType, documentCategory);
      }
      
      setCurrentUploadId(uploadId);
      
      // Subscribe to progress updates
      const unsubscribe = subscribe(uploadId, (progress: UploadProgress) => {
        setUploadProgress(progress);
        
        // Handle completion
        if (progress.state === 'success') {
          // The document will be handled by the upload context
          setCurrentUploadId(null);
          setUploadProgress(null);
          unsubscribe();
        } else if (progress.state === 'error') {
          handleUploadError(new Error(progress.error || 'Upload failed'));
          unsubscribe();
        }
      });
      
    } catch (error) {
      handleUploadError(error instanceof Error ? error : new Error('Upload failed'));
    }
  }, [
    currentUploadId,
    disabled,
    isLowMemoryMode,
    uploadImage,
    uploadDocument,
    subscribe,
    handleUploadError
  ]);

  // Retry handler for error boundary
  const handleRetry = useCallback(() => {
    console.log('[UltimateDocumentUploader] Retrying upload');
    setRetryCount(prev => prev + 1);
    
    // Reset state
    setCurrentUploadId(null);
    setUploadProgress(null);
    
    // Clear any completed uploads
    // clearCompleted(); // Commented out to prevent clearing other uploads
  }, []);

  // Enhanced document uploaded handler
  const enhancedDocumentUploaded = useCallback((document: Document) => {
    // Validate document
    if (!document || !document.id) {
      handleUploadError(new Error('Invalid document received'));
      return;
    }

    // Check file size
    if (document.fileSize && document.fileSize > maxFileSize) {
      handleUploadError(new Error(`File size (${Math.round(document.fileSize / 1024 / 1024)}MB) exceeds limit (${Math.round(maxFileSize / 1024 / 1024)}MB)`));
      return;
    }

    // Check file type
    if (!allowedFileTypes.includes(document.fileType)) {
      handleUploadError(new Error(`File type '${document.fileType}' is not allowed. Allowed types: ${allowedFileTypes.join(', ')}`));
      return;
    }

    handleUploadComplete(document);
  }, [maxFileSize, allowedFileTypes, handleUploadComplete, handleUploadError]);

  // Memoized styles
  const styles = React.useMemo(() => StyleSheet.create({
    container: {
      padding: spacing.md,
    },
    errorContainer: {
      padding: spacing.lg,
      backgroundColor: colors.error[50],
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.error[200],
    },
  }), [colors, spacing]);

  return (
    <View style={styles.container}>
      <ErrorBoundary
        onError={(error, errorInfo) => {
          console.error('[UltimateDocumentUploader] Component error:', error, errorInfo);
          MemoryManager.forceCleanup();
        }}
        resetKeys={[retryCount]}
        resetOnPropsChange={true}
      >
        <UploadErrorBoundary
          uploadType={uploadType}
          onRetry={handleRetry}
        >
          <OptimizedDocumentUploader
            onDocumentUploaded={enhancedDocumentUploaded}
            preselectedDocumentType={preselectedDocumentType}
            disabled={disabled || !!currentUploadId}
            maxFileSize={maxFileSize}
            allowedFileTypes={allowedFileTypes}
          />
        </UploadErrorBoundary>
      </ErrorBoundary>
    </View>
  );
});

UltimateDocumentUploader.displayName = 'UltimateDocumentUploader';

export default UltimateDocumentUploader;
