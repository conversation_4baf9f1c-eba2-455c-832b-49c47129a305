#!/usr/bin/env node

/**
 * Backend Integration Test Script
 * 
 * This script tests the backend integration issues directly
 * without needing to run the full React Native app.
 */

const axios = require('axios');

const BASE_URL = 'https://inerca-backend-wild-leaf-8326.fly.dev';

console.log('🔍 Backend Integration Diagnostics');
console.log('=====================================');
console.log(`Testing backend at: ${BASE_URL}`);
console.log('');

async function testConnectivity() {
  console.log('1. Testing basic connectivity...');
  try {
    const response = await axios.get(BASE_URL, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Inerca-Mobile-App/1.0.0',
        'Accept': 'application/json'
      }
    });
    console.log('   ✅ Basic connectivity: OK');
    console.log(`   Status: ${response.status} ${response.statusText}`);
  } catch (error) {
    console.log('   ❌ Basic connectivity: FAILED');
    console.log(`   Error: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
    }
  }
  console.log('');
}

async function testHealthEndpoint() {
  console.log('2. Testing health endpoint...');
  try {
    const response = await axios.get(`${BASE_URL}/api/v1/health`, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Inerca-Mobile-App/1.0.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    console.log('   ✅ Health endpoint: OK');
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
  } catch (error) {
    console.log('   ❌ Health endpoint: FAILED');
    console.log(`   Error: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
      console.log(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
  console.log('');
}

async function testCORS() {
  console.log('3. Testing CORS configuration...');
  try {
    const response = await axios.options(`${BASE_URL}/api/v1/health`, {
      timeout: 10000,
      headers: {
        'Origin': 'http://localhost:8081',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type,Authorization'
      }
    });
    console.log('   ✅ CORS preflight: OK');
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    const corsHeaders = {
      'Access-Control-Allow-Origin': response.headers['access-control-allow-origin'],
      'Access-Control-Allow-Methods': response.headers['access-control-allow-methods'],
      'Access-Control-Allow-Headers': response.headers['access-control-allow-headers'],
      'Access-Control-Allow-Credentials': response.headers['access-control-allow-credentials']
    };
    console.log('   CORS Headers:', JSON.stringify(corsHeaders, null, 2));
  } catch (error) {
    console.log('   ❌ CORS preflight: FAILED');
    console.log(`   Error: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
    }
  }
  console.log('');
}

async function testAuthEndpoint() {
  console.log('4. Testing authentication endpoint...');
  try {
    const response = await axios.get(`${BASE_URL}/api/v1/user/me`, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Inerca-Mobile-App/1.0.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    console.log('   ⚠️  Auth endpoint (no token): Unexpected success');
    console.log(`   Status: ${response.status} ${response.statusText}`);
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('   ✅ Auth endpoint (no token): Correctly returns 401');
      console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
    } else {
      console.log('   ❌ Auth endpoint (no token): Unexpected error');
      console.log(`   Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
      }
    }
  }
  console.log('');
}

async function testRequestLogging() {
  console.log('5. Testing request logging...');
  const uniqueId = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    const response = await axios.get(`${BASE_URL}/api/v1/health`, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Inerca-Mobile-App/1.0.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Request-ID': uniqueId,
        'X-Client': 'Inerca-Mobile-App',
        'X-Version': '1.0.0',
        'X-Test': 'Backend-Integration-Test'
      }
    });
    console.log('   ✅ Request logging test: OK');
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   🔍 IMPORTANT: Backend dev should look for request with ID: ${uniqueId}`);
    console.log(`   🔍 Request headers included: X-Request-ID, X-Client, X-Version, X-Test`);
  } catch (error) {
    console.log('   ❌ Request logging test: FAILED');
    console.log(`   Error: ${error.message}`);
    console.log(`   🔍 Backend dev should still look for request with ID: ${uniqueId}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
    }
  }
  console.log('');
}

async function testLoginEndpoint() {
  console.log('6. Testing login endpoint...');
  try {
    const formData = new URLSearchParams();
    formData.append('username', '<EMAIL>');
    formData.append('password', 'testpassword');

    const response = await axios.post(`${BASE_URL}/api/v1/login`, formData, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Inerca-Mobile-App/1.0.0',
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    console.log('   ⚠️  Login endpoint: Unexpected success (test credentials should fail)');
    console.log(`   Status: ${response.status} ${response.statusText}`);
  } catch (error) {
    if (error.response && (error.response.status === 401 || error.response.status === 422)) {
      console.log('   ✅ Login endpoint: Correctly rejects invalid credentials');
      console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
    } else {
      console.log('   ❌ Login endpoint: Unexpected error');
      console.log(`   Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
        console.log(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }
  }
  console.log('');
}

async function runAllTests() {
  console.log(`Started at: ${new Date().toISOString()}`);
  console.log('');
  
  await testConnectivity();
  await testHealthEndpoint();
  await testCORS();
  await testAuthEndpoint();
  await testRequestLogging();
  await testLoginEndpoint();
  
  console.log('=====================================');
  console.log('🏁 Diagnostics Complete');
  console.log('');
  console.log('📋 Summary for Backend Developer:');
  console.log('1. Check server logs for requests with X-Request-ID headers');
  console.log('2. Verify CORS configuration allows mobile app origins');
  console.log('3. Ensure request logging is enabled for all incoming requests');
  console.log('4. Check if the server is properly receiving and processing requests');
  console.log('');
  console.log('🔧 Next Steps:');
  console.log('1. Share this output with the backend developer');
  console.log('2. Ask them to check logs during the time this test ran');
  console.log('3. Verify server configuration and logging setup');
  console.log(`4. Test completed at: ${new Date().toISOString()}`);
}

// Run the tests
runAllTests().catch(error => {
  console.error('Fatal error running tests:', error);
  process.exit(1);
});
