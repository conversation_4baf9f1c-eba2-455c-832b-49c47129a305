import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface DeveloperState {
  isEnabled: boolean;
  unlockAttempts: number;
  lastUnlockAttempt: number;
  features: {
    apiTesting: boolean;
    backendDiagnostics: boolean;
    mockDataToggle: boolean;
    performanceMonitoring: boolean;
    logViewer: boolean;
    stateInspector: boolean;
  };
  settings: {
    showRequestLogs: boolean;
    showResponseLogs: boolean;
    enableVerboseLogging: boolean;
    autoRunHealthChecks: boolean;
  };
}

const initialState: DeveloperState = {
  isEnabled: false,
  unlockAttempts: 0,
  lastUnlockAttempt: 0,
  features: {
    apiTesting: true,
    backendDiagnostics: true,
    mockDataToggle: true,
    performanceMonitoring: true,
    logViewer: true,
    stateInspector: true,
  },
  settings: {
    showRequestLogs: true,
    showResponseLogs: true,
    enableVerboseLogging: false,
    autoRunHealthChecks: false,
  },
};

const developerSlice = createSlice({
  name: 'developer',
  initialState,
  reducers: {
    enableDeveloperMode: (state) => {
      state.isEnabled = true;
      state.unlockAttempts = 0;
      // Persist to storage
      AsyncStorage.setItem('developer_mode_enabled', 'true');
      console.log('[Developer Mode] Enabled');
    },
    
    disableDeveloperMode: (state) => {
      state.isEnabled = false;
      state.unlockAttempts = 0;
      // Remove from storage
      AsyncStorage.removeItem('developer_mode_enabled');
      console.log('[Developer Mode] Disabled');
    },
    
    incrementUnlockAttempts: (state) => {
      state.unlockAttempts += 1;
      state.lastUnlockAttempt = Date.now();
      
      // Reset attempts after 5 minutes
      if (state.unlockAttempts >= 10) {
        setTimeout(() => {
          state.unlockAttempts = 0;
        }, 5 * 60 * 1000);
      }
    },
    
    resetUnlockAttempts: (state) => {
      state.unlockAttempts = 0;
      state.lastUnlockAttempt = 0;
    },
    
    toggleFeature: (state, action: PayloadAction<keyof DeveloperState['features']>) => {
      const feature = action.payload;
      state.features[feature] = !state.features[feature];
      console.log(`[Developer Mode] Feature ${feature}: ${state.features[feature] ? 'enabled' : 'disabled'}`);
    },
    
    updateSetting: (state, action: PayloadAction<{
      setting: keyof DeveloperState['settings'];
      value: boolean;
    }>) => {
      const { setting, value } = action.payload;
      state.settings[setting] = value;
      console.log(`[Developer Mode] Setting ${setting}: ${value}`);
    },
    
    loadDeveloperState: (state, action: PayloadAction<Partial<DeveloperState>>) => {
      return { ...state, ...action.payload };
    },
  },
});

export const {
  enableDeveloperMode,
  disableDeveloperMode,
  incrementUnlockAttempts,
  resetUnlockAttempts,
  toggleFeature,
  updateSetting,
  loadDeveloperState,
} = developerSlice.actions;

export default developerSlice.reducer;

// Selectors
export const selectDeveloperMode = (state: { developer: DeveloperState }) => state.developer;
export const selectIsDeveloperEnabled = (state: { developer: DeveloperState }) => state.developer.isEnabled;
export const selectDeveloperFeatures = (state: { developer: DeveloperState }) => state.developer.features;
export const selectDeveloperSettings = (state: { developer: DeveloperState }) => state.developer.settings;

// Async actions for persistence
export const initializeDeveloperMode = () => async (dispatch: any) => {
  try {
    const isEnabled = await AsyncStorage.getItem('developer_mode_enabled');
    if (isEnabled === 'true') {
      dispatch(enableDeveloperMode());
    }
  } catch (error) {
    console.error('[Developer Mode] Failed to load state:', error);
  }
};

// Developer mode unlock sequences
export const UNLOCK_SEQUENCES = {
  // Tap sequence: tap version number 7 times quickly
  VERSION_TAP: {
    requiredTaps: 7,
    timeWindow: 3000, // 3 seconds
    description: 'Tap version number 7 times quickly'
  },
  
  // Gesture sequence: specific pattern of swipes
  GESTURE_PATTERN: {
    pattern: ['up', 'up', 'down', 'down', 'left', 'right', 'left', 'right'],
    timeWindow: 10000, // 10 seconds
    description: 'Swipe: up, up, down, down, left, right, left, right'
  },
  
  // Long press combination
  LONG_PRESS: {
    duration: 5000, // 5 seconds
    description: 'Long press app logo for 5 seconds'
  }
};

// Utility functions
export const isDeveloperModeAvailable = () => {
  // Only available in development or specific build configurations
  return __DEV__ || process.env.NODE_ENV === 'development';
};

export const shouldShowDeveloperFeatures = (state: DeveloperState) => {
  return isDeveloperModeAvailable() && state.isEnabled;
};
