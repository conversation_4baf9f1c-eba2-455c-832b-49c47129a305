import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, Upload, AlertCircle } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import Animated, { FadeIn } from 'react-native-reanimated';
import useApplicationStore from '@/store/applicationStore';
import usePaymentStore from '@/store/paymentStore';
import UltimateDocumentUploader from '@/components/documents/UltimateDocumentUploader';
import { Document } from '@/components/documents/types';
import BottomNavBar from '@/components/navigation/BottomNavBar';

export default function UploadPaymentProofScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const params = useLocalSearchParams();
  const applicationId = params.applicationId as string;

  const { getApplicationById, uploadPaymentProof } = useApplicationStore();
  const { addPaymentProof } = usePaymentStore();

  const [application, setApplication] = useState<any>(applicationId ? getApplicationById(applicationId) : null);
  const [uploadStatus, setUploadStatus] = useState<'pending' | 'uploading' | 'success' | 'error'>('pending');
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (applicationId) {
      const app = getApplicationById(applicationId);
      setApplication(app);
    }
  }, [applicationId, getApplicationById]);

  // Handle document uploaded
  const handleDocumentUploaded = async (document: Document) => {
    if (!application) return;

    setUploadStatus('uploading');

    try {
      // Add payment proof to payment store
      await addPaymentProof({
        applicationId: application.id,
        reference: application.reference,
        amount: application.payment?.amount || application.premium,
        currency: application.payment?.currency || application.currency,
        date: new Date().toISOString().split('T')[0],
        documentId: document.id
      });

      // Update application with payment proof
      await uploadPaymentProof(application.id, document.id);

      // Update local application state
      const updatedApp = getApplicationById(application.id);
      setApplication(updatedApp);

      setUploadStatus('success');

      // Navigate back to application detail after 2 seconds
      setTimeout(() => {
        router.push(`/applications/${application.id}`);
      }, 2000);
    } catch (error) {
      console.error('Error uploading payment proof:', error);
      setUploadStatus('error');
      setErrorMessage('Failed to upload payment proof. Please try again.');
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
    },
    backButton: {
      marginRight: spacing.sm,
    },
    title: {
      ...typography.h3,
      color: colors.text,
      flex: 1,
    },
    content: {
      flex: 1,
      padding: spacing.md,
    },
    referenceContainer: {
      backgroundColor: colors.primary[50],
      borderRadius: 8,
      padding: spacing.md,
      marginBottom: spacing.md,
    },
    referenceLabel: {
      ...typography.caption,
      color: colors.primary[700],
      marginBottom: spacing.xs,
    },
    referenceValue: {
      ...typography.h4,
      color: colors.primary[700],
      fontWeight: 'bold',
    },
    statusContainer: {
      padding: spacing.md,
      borderRadius: 8,
      marginTop: spacing.md,
      marginBottom: spacing.md,
    },
    statusText: {
      ...typography.body,
      textAlign: 'center',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    emptyText: {
      ...typography.body,
      color: colors.textSecondary,
      marginTop: spacing.md,
      textAlign: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Upload Payment Proof</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeIn.duration(300)}>
          {application ? (
            <View>
              <View style={styles.referenceContainer}>
                <Text style={styles.referenceLabel}>Payment Reference</Text>
                <Text style={styles.referenceValue}>{application.reference}</Text>
              </View>

              <UltimateDocumentUploader
                onDocumentUploaded={handleDocumentUploaded}
                preselectedDocumentType="Payment Proof - Premium"
                maxFileSize={10 * 1024 * 1024}
                allowedFileTypes={['image', 'pdf', 'doc']}
                uploadType="document"
              />

              {uploadStatus !== 'pending' && (
                <View style={[
                  styles.statusContainer,
                  {
                    backgroundColor: uploadStatus === 'success'
                      ? `${colors.success[500]}15`
                      : uploadStatus === 'error'
                        ? `${colors.error[500]}15`
                        : `${colors.primary[500]}15`
                  }
                ]}>
                  <Text style={[
                    styles.statusText,
                    {
                      color: uploadStatus === 'success'
                        ? colors.success[500]
                        : uploadStatus === 'error'
                          ? colors.error[500]
                          : colors.primary[500]
                    }
                  ]}>
                    {uploadStatus === 'uploading' && 'Uploading...'}
                    {uploadStatus === 'success' && 'Upload successful!'}
                    {uploadStatus === 'error' && (errorMessage || 'Upload failed. Please try again.')}
                  </Text>
                </View>
              )}
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <AlertCircle size={48} color={colors.textSecondary} />
              <Text style={styles.emptyText}>No application selected</Text>
            </View>
          )}
        </Animated.View>
      </ScrollView>

      <BottomNavBar />
    </SafeAreaView>
  );
}
