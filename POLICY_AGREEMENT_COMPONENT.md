# PolicyAgreementComponent Documentation

## Overview

The `PolicyAgreementComponent` is a reusable React Native component that displays the exact legal declaration text required for insurance applications and ensures mandatory user acceptance before form submission.

## Features

### ✅ **Legal Compliance**
- Displays the exact required legal declaration text
- Ensures users read the complete declaration before agreeing
- Provides legally binding acceptance mechanism

### ✅ **User Experience**
- Scrollable text area with scroll-to-read requirement
- Visual indicators for scroll progress
- Disabled state until user scrolls to bottom
- Clear visual feedback for agreement state

### ✅ **Accessibility**
- Screen reader support with proper accessibility labels
- Keyboard navigation support
- High contrast design for visibility
- Semantic HTML roles for form elements

### ✅ **Integration Ready**
- Reusable across all form types
- Callback-based state management
- Customizable styling and behavior
- TypeScript support with proper interfaces

## Legal Declaration Text

The component displays this exact text as required:

```
Declaration and Terms of Submission

I hereby declare that the information provided in this form is true, complete, and correct to the best of my knowledge. I understand that any false or misleading information may result in the rejection of this application, cancellation of the policy, or denial of any claims made.

I acknowledge that:
• This form, once submitted, constitutes a legal and binding declaration.
• This document may be used as admissible evidence in a court of law or arbitration proceedings, should any dispute arise.
• I consent to the insurance company and/or its appointed representatives verifying any information provided herein.
• I accept that the insurer reserves the right to request additional documentation or clarification as deemed necessary.
• I understand that cover shall only be effective upon acceptance by the insurer and subject to the terms and conditions of the policy issued.
• I indemnify the insurer and its intermediaries against any loss or liability arising from inaccuracies in the information provided by me.

By submitting this form, I confirm that I have read, understood, and agree to be bound by the terms, conditions, and privacy policies of the insurer.
```

## Usage

### Basic Implementation

```typescript
import PolicyAgreementComponent from '@/components/PolicyAgreementComponent';

function MyForm() {
  const [isAgreed, setIsAgreed] = useState(false);

  const handleAgreementChange = (agreed: boolean) => {
    setIsAgreed(agreed);
  };

  return (
    <PolicyAgreementComponent
      onAgreementChange={handleAgreementChange}
      isRequired={true}
    />
  );
}
```

### Props Interface

```typescript
interface PolicyAgreementComponentProps {
  onAgreementChange: (agreed: boolean) => void;  // Callback when agreement state changes
  isRequired?: boolean;                          // Show required indicator (default: true)
  disabled?: boolean;                           // Disable interaction (default: false)
  style?: any;                                 // Custom container styles
}
```

### Integration Examples

#### 1. Quote Submission Forms
```typescript
// In quote forms - prevent submission until agreed
<PolicyAgreementComponent
  onAgreementChange={setQuoteAgreed}
  isRequired={true}
/>
<TouchableOpacity
  disabled={!quoteAgreed}
  onPress={submitQuote}
>
  <Text>Submit Quote Request</Text>
</TouchableOpacity>
```

#### 2. Policy Application Forms
```typescript
// In application forms - required for policy binding
<PolicyAgreementComponent
  onAgreementChange={setPolicyAgreed}
  isRequired={true}
  disabled={isSubmitting}
/>
```

#### 3. Claims Submission Forms
```typescript
// In claims forms - legal declaration for claim validity
<PolicyAgreementComponent
  onAgreementChange={setClaimAgreed}
  isRequired={true}
/>
```

## Component Behavior

### Scroll-to-Read Requirement
1. User must scroll to the bottom of the declaration text
2. Checkbox remains disabled until scroll completion
3. Visual indicator shows scroll requirement
4. Smooth user experience with clear guidance

### Agreement State Management
1. Component tracks internal agreement state
2. Calls `onAgreementChange` callback when state changes
3. Parent component receives boolean value
4. Can be used to control form submission

### Visual States
- **Default**: Checkbox disabled, scroll indicator visible
- **Scrolled**: Checkbox enabled, scroll indicator hidden
- **Agreed**: Checkbox checked, ready for submission
- **Disabled**: All interactions disabled (loading states)

## Styling

### Default Theme Integration
- Uses app's color constants from `@/constants/Colors`
- Matches existing form component styling
- Responsive design for different screen sizes
- Consistent with app's design system

### Customization Options
```typescript
// Custom container styling
<PolicyAgreementComponent
  style={{
    marginVertical: 20,
    backgroundColor: 'custom-color',
    borderRadius: 16,
  }}
  onAgreementChange={handleChange}
/>
```

## File Locations

- **Component**: `components/PolicyAgreementComponent.tsx`
- **Example**: `components/examples/PolicyAgreementExample.tsx`
- **Documentation**: `POLICY_AGREEMENT_COMPONENT.md`

## Integration Points

### Current Integration Opportunities
1. **Quote Forms**: Motor, Houseowners, Household Contents, All Risks, Life Assurance
2. **Application Forms**: Policy application submissions
3. **Claims Forms**: Claims submission processes
4. **Document Upload**: Legal acceptance for document verification

### Future Integration
1. **Policy Renewal**: Terms acceptance for renewals
2. **Profile Updates**: Legal confirmation for profile changes
3. **Payment Processing**: Terms for payment authorization
4. **Document Sharing**: Legal consent for document sharing

## Testing

### Manual Testing Checklist
- [ ] Text displays correctly and completely
- [ ] Scroll functionality works on all devices
- [ ] Checkbox enables only after scrolling to bottom
- [ ] Agreement state changes trigger callback
- [ ] Disabled state prevents all interactions
- [ ] Accessibility features work with screen readers
- [ ] Component integrates properly in forms
- [ ] Required indicator displays when needed

### Automated Testing
```typescript
// Example test cases
describe('PolicyAgreementComponent', () => {
  it('should disable checkbox until scrolled to bottom');
  it('should call onAgreementChange when agreement toggles');
  it('should display required indicator when isRequired=true');
  it('should disable all interactions when disabled=true');
  it('should have proper accessibility labels');
});
```

## Compliance Notes

### Legal Requirements
- Text must be displayed exactly as provided
- User must actively scroll and read the declaration
- Agreement must be explicitly confirmed by user action
- Component provides audit trail of user acceptance

### Data Protection
- No personal data is stored in the component
- Agreement state is managed by parent component
- Complies with privacy requirements
- Suitable for GDPR and similar regulations
