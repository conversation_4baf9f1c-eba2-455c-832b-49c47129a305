/**
 * Optimized Components - Performance-enhanced versions of common components
 * 
 * These components demonstrate best practices for React Native performance:
 * - React.memo for preventing unnecessary rerenders
 * - useMemo for expensive calculations
 * - useCallback for stable function references
 * - Proper prop drilling avoidance
 */

import React, { memo, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  ListRenderItem,
} from 'react-native';
import { Colors } from '@/constants/Colors';

// ============================================================================
// OPTIMIZED POLICY CARD
// ============================================================================

interface Policy {
  id: string;
  type: string;
  premium: number;
  status: string;
  expiryDate: string;
}

interface OptimizedPolicyCardProps {
  policy: Policy;
  onPress: (policyId: string) => void;
  isSelected?: boolean;
}

const OptimizedPolicyCard = memo<OptimizedPolicyCardProps>(({ 
  policy, 
  onPress, 
  isSelected = false 
}) => {
  // Memoize expensive calculations
  const formattedPremium = useMemo(() => {
    return new Intl.NumberFormat('en-BW', {
      style: 'currency',
      currency: 'BWP',
      minimumFractionDigits: 2,
    }).format(policy.premium);
  }, [policy.premium]);

  const statusColor = useMemo(() => {
    switch (policy.status) {
      case 'active': return Colors.success;
      case 'expired': return Colors.error;
      case 'pending': return Colors.warning;
      default: return Colors.textSecondary;
    }
  }, [policy.status]);

  const expiryText = useMemo(() => {
    const expiryDate = new Date(policy.expiryDate);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysUntilExpiry < 0) return 'Expired';
    if (daysUntilExpiry < 30) return `Expires in ${daysUntilExpiry} days`;
    return `Expires ${expiryDate.toLocaleDateString()}`;
  }, [policy.expiryDate]);

  // Stable callback reference
  const handlePress = useCallback(() => {
    onPress(policy.id);
  }, [onPress, policy.id]);

  return (
    <TouchableOpacity
      style={[styles.policyCard, isSelected && styles.policyCardSelected]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.policyHeader}>
        <Text style={styles.policyType}>{policy.type}</Text>
        <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
          <Text style={styles.statusText}>{policy.status}</Text>
        </View>
      </View>
      
      <Text style={styles.premiumText}>{formattedPremium}</Text>
      <Text style={styles.expiryText}>{expiryText}</Text>
    </TouchableOpacity>
  );
});

OptimizedPolicyCard.displayName = 'OptimizedPolicyCard';

// ============================================================================
// OPTIMIZED POLICY LIST
// ============================================================================

interface OptimizedPolicyListProps {
  policies: Policy[];
  onPolicyPress: (policyId: string) => void;
  selectedPolicyId?: string;
  refreshing?: boolean;
  onRefresh?: () => void;
}

const OptimizedPolicyList = memo<OptimizedPolicyListProps>(({
  policies,
  onPolicyPress,
  selectedPolicyId,
  refreshing = false,
  onRefresh,
}) => {
  // Memoize the render item function to prevent recreating on every render
  const renderPolicyItem: ListRenderItem<Policy> = useCallback(({ item }) => (
    <OptimizedPolicyCard
      policy={item}
      onPress={onPolicyPress}
      isSelected={item.id === selectedPolicyId}
    />
  ), [onPolicyPress, selectedPolicyId]);

  // Memoize the key extractor
  const keyExtractor = useCallback((item: Policy) => item.id, []);

  // Memoize the item separator
  const ItemSeparator = useMemo(() => (
    <View style={styles.separator} />
  ), []);

  // Memoize empty component
  const EmptyComponent = useMemo(() => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>No policies found</Text>
    </View>
  ), []);

  return (
    <FlatList
      data={policies}
      renderItem={renderPolicyItem}
      keyExtractor={keyExtractor}
      ItemSeparatorComponent={() => ItemSeparator}
      ListEmptyComponent={EmptyComponent}
      refreshing={refreshing}
      onRefresh={onRefresh}
      removeClippedSubviews={true} // Performance optimization for long lists
      maxToRenderPerBatch={10} // Render 10 items per batch
      windowSize={10} // Keep 10 screens worth of items in memory
      initialNumToRender={8} // Render 8 items initially
      getItemLayout={(data, index) => ({
        length: 120, // Fixed item height for better performance
        offset: 120 * index,
        index,
      })}
    />
  );
});

OptimizedPolicyList.displayName = 'OptimizedPolicyList';

// ============================================================================
// OPTIMIZED FORM FIELD
// ============================================================================

interface OptimizedFormFieldProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  error?: string;
  required?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
}

const OptimizedFormField = memo<OptimizedFormFieldProps>(({
  label,
  value,
  onChangeText,
  placeholder,
  error,
  required = false,
  keyboardType = 'default',
}) => {
  // Memoize styles based on error state
  const inputStyle = useMemo(() => [
    styles.input,
    error && styles.inputError,
  ], [error]);

  const labelStyle = useMemo(() => [
    styles.label,
    error && styles.labelError,
  ], [error]);

  return (
    <View style={styles.formField}>
      <Text style={labelStyle}>
        {label}
        {required && <Text style={styles.required}> *</Text>}
      </Text>
      
      <TextInput
        style={inputStyle}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        keyboardType={keyboardType}
        placeholderTextColor={Colors.textSecondary}
      />
      
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
});

OptimizedFormField.displayName = 'OptimizedFormField';

// ============================================================================
// OPTIMIZED BUTTON
// ============================================================================

interface OptimizedButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
}

const OptimizedButton = memo<OptimizedButtonProps>(({
  title,
  onPress,
  variant = 'primary',
  disabled = false,
  loading = false,
  icon,
}) => {
  // Memoize button styles
  const buttonStyle = useMemo(() => [
    styles.button,
    styles[`button${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    (disabled || loading) && styles.buttonDisabled,
  ], [variant, disabled, loading]);

  const textStyle = useMemo(() => [
    styles.buttonText,
    styles[`buttonText${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    (disabled || loading) && styles.buttonTextDisabled,
  ], [variant, disabled, loading]);

  // Stable callback
  const handlePress = useCallback(() => {
    if (!disabled && !loading) {
      onPress();
    }
  }, [onPress, disabled, loading]);

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {icon && <View style={styles.buttonIcon}>{icon}</View>}
      <Text style={textStyle}>
        {loading ? 'Loading...' : title}
      </Text>
    </TouchableOpacity>
  );
});

OptimizedButton.displayName = 'OptimizedButton';

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
  // Policy Card Styles
  policyCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  policyCardSelected: {
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  policyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  policyType: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.white,
    textTransform: 'capitalize',
  },
  premiumText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 4,
  },
  expiryText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },

  // List Styles
  separator: {
    height: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },

  // Form Field Styles
  formField: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  labelError: {
    color: Colors.error,
  },
  required: {
    color: Colors.error,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: Colors.inputBackground,
  },
  inputError: {
    borderColor: Colors.error,
  },
  errorText: {
    fontSize: 14,
    color: Colors.error,
    marginTop: 4,
  },

  // Button Styles
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonPrimary: {
    backgroundColor: Colors.primary,
  },
  buttonSecondary: {
    backgroundColor: Colors.secondary,
  },
  buttonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  buttonDisabled: {
    backgroundColor: Colors.disabled,
    borderColor: Colors.disabled,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  buttonTextPrimary: {
    color: Colors.white,
  },
  buttonTextSecondary: {
    color: Colors.white,
  },
  buttonTextOutline: {
    color: Colors.primary,
  },
  buttonTextDisabled: {
    color: Colors.textSecondary,
  },
  buttonIcon: {
    marginRight: 8,
  },
});

export {
  OptimizedPolicyCard,
  OptimizedPolicyList,
  OptimizedFormField,
  OptimizedButton,
};
