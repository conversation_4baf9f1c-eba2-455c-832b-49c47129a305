import React, { useState, useCallback, useRef, memo, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Upload, Camera, Image as ImageIcon, File } from 'lucide-react-native';
import { Document, DocumentCategory } from './types';
import { UploadManager, UploadResult, UploadProgress } from '@/utils/UploadManager';
import DocumentTypeSelector from './DocumentTypeSelector';
import Toast from 'react-native-toast-message';

interface OptimizedDocumentUploaderProps {
  onDocumentUploaded: (document: Document) => void;
  preselectedDocumentType?: string;
  disabled?: boolean;
  maxFileSize?: number;
  allowedFileTypes?: string[];
}

// Memoized upload option component
const UploadOption = memo<{
  icon: React.ReactNode;
  text: string;
  onPress: () => void;
  disabled: boolean;
  colors: any;
}>(({ icon, text, onPress, disabled, colors }) => {
  const styles = useMemo(() => StyleSheet.create({
    uploadOption: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 12,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: disabled ? colors.gray[300] : colors.primary[200],
      backgroundColor: disabled ? colors.gray[100] : colors.primary[50],
      marginHorizontal: 4,
    },
    uploadOptionText: {
      fontSize: 14,
      fontWeight: '500',
      color: disabled ? colors.gray[400] : colors.primary[700],
      marginTop: 8,
      textAlign: 'center',
    },
  }), [colors, disabled]);

  return (
    <TouchableOpacity
      style={styles.uploadOption}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      {icon}
      <Text style={styles.uploadOptionText}>{text}</Text>
    </TouchableOpacity>
  );
});

UploadOption.displayName = 'UploadOption';

// Memoized progress indicator
const ProgressIndicator = memo<{
  progress: UploadProgress;
  colors: any;
}>(({ progress, colors }) => {
  const styles = useMemo(() => StyleSheet.create({
    progressContainer: {
      padding: 16,
      borderRadius: 12,
      backgroundColor: colors.primary[50],
      borderWidth: 1,
      borderColor: colors.primary[200],
      marginTop: 16,
    },
    progressText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.primary[700],
      textAlign: 'center',
      marginBottom: 8,
    },
    progressBar: {
      height: 4,
      backgroundColor: colors.primary[200],
      borderRadius: 2,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: colors.primary[500],
      borderRadius: 2,
    },
    errorText: {
      fontSize: 12,
      color: colors.error[500],
      textAlign: 'center',
      marginTop: 4,
    },
  }), [colors]);

  const getProgressText = () => {
    switch (progress.state) {
      case 'preparing': return 'Preparing...';
      case 'selecting': return 'Opening picker...';
      case 'processing': return 'Processing file...';
      case 'uploading': return 'Uploading...';
      case 'success': return 'Upload complete!';
      case 'error': return 'Upload failed';
      default: return 'Processing...';
    }
  };

  return (
    <View style={styles.progressContainer}>
      <Text style={styles.progressText}>{getProgressText()}</Text>
      <View style={styles.progressBar}>
        <View 
          style={[
            styles.progressFill, 
            { width: `${progress.progress}%` }
          ]} 
        />
      </View>
      {progress.error && (
        <Text style={styles.errorText}>{progress.error}</Text>
      )}
    </View>
  );
});

ProgressIndicator.displayName = 'ProgressIndicator';

// Main optimized document uploader component
const OptimizedDocumentUploader: React.FC<OptimizedDocumentUploaderProps> = memo(({
  onDocumentUploaded,
  preselectedDocumentType,
  disabled = false,
  maxFileSize = 10 * 1024 * 1024, // 10MB default
  allowedFileTypes = ['image', 'pdf', 'doc']
}) => {
  const { isDarkMode } = useTheme();
  
  // Memoize theme to prevent recalculation
  const theme = useMemo(() => createTheme(isDarkMode), [isDarkMode]);
  const { colors, spacing, typography, borders } = theme;

  // State management with refs for performance
  const [selectedDocumentType, setSelectedDocumentType] = useState<string | null>(
    preselectedDocumentType || null
  );
  const [selectedDocumentCategory, setSelectedDocumentCategory] = useState<DocumentCategory | null>(null);
  const [currentUploadId, setCurrentUploadId] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);

  // Refs for stable references
  const onDocumentUploadedRef = useRef(onDocumentUploaded);
  onDocumentUploadedRef.current = onDocumentUploaded;

  // Memoized upload callback
  const handleUploadResult = useCallback((result: UploadResult) => {
    console.log('[OptimizedDocumentUploader] Upload result:', result);
    
    if (result.success && result.document) {
      // Validate file size
      if (result.document.fileSize && result.document.fileSize > maxFileSize) {
        Toast.show({
          type: 'error',
          text1: 'File Too Large',
          text2: `File size must be less than ${Math.round(maxFileSize / 1024 / 1024)}MB`,
          visibilityTime: 4000,
        });
        return;
      }

      // Validate file type
      if (!allowedFileTypes.includes(result.document.fileType)) {
        Toast.show({
          type: 'error',
          text1: 'Invalid File Type',
          text2: `Only ${allowedFileTypes.join(', ')} files are allowed`,
          visibilityTime: 4000,
        });
        return;
      }

      onDocumentUploadedRef.current(result.document);
      
      Toast.show({
        type: 'success',
        text1: 'Upload Successful',
        text2: 'Document has been uploaded successfully',
        visibilityTime: 3000,
      });
    } else if (result.error && result.error !== 'User canceled') {
      Toast.show({
        type: 'error',
        text1: 'Upload Failed',
        text2: result.error,
        visibilityTime: 4000,
      });
    }

    // Clear upload state
    setCurrentUploadId(null);
    setUploadProgress(null);
  }, [maxFileSize, allowedFileTypes]);

  // Memoized progress callback
  const handleProgressUpdate = useCallback((progress: UploadProgress) => {
    setUploadProgress(progress);
  }, []);

  // Memoized document type selection handler
  const handleDocumentTypeSelect = useCallback((
    documentType: string,
    documentCategory: DocumentCategory
  ) => {
    setSelectedDocumentType(documentType);
    setSelectedDocumentCategory(documentCategory);
  }, []);

  // Memoized upload handlers
  const handleCameraUpload = useCallback(() => {
    if (!selectedDocumentType || !selectedDocumentCategory || disabled || currentUploadId) {
      return;
    }

    const uploadId = UploadManager.pickImage(
      true, // useCamera
      selectedDocumentType,
      selectedDocumentCategory,
      handleUploadResult
    );

    setCurrentUploadId(uploadId);
    UploadManager.registerProgressCallback(uploadId, handleProgressUpdate);
  }, [selectedDocumentType, selectedDocumentCategory, disabled, currentUploadId, handleUploadResult, handleProgressUpdate]);

  const handleGalleryUpload = useCallback(() => {
    if (!selectedDocumentType || !selectedDocumentCategory || disabled || currentUploadId) {
      return;
    }

    const uploadId = UploadManager.pickImage(
      false, // useCamera
      selectedDocumentType,
      selectedDocumentCategory,
      handleUploadResult
    );

    setCurrentUploadId(uploadId);
    UploadManager.registerProgressCallback(uploadId, handleProgressUpdate);
  }, [selectedDocumentType, selectedDocumentCategory, disabled, currentUploadId, handleUploadResult, handleProgressUpdate]);

  const handleDocumentUpload = useCallback(() => {
    if (!selectedDocumentType || !selectedDocumentCategory || disabled || currentUploadId) {
      return;
    }

    const uploadId = UploadManager.pickDocument(
      selectedDocumentType,
      selectedDocumentCategory,
      handleUploadResult
    );

    setCurrentUploadId(uploadId);
    UploadManager.registerProgressCallback(uploadId, handleProgressUpdate);
  }, [selectedDocumentType, selectedDocumentCategory, disabled, currentUploadId, handleUploadResult, handleProgressUpdate]);

  // Memoized styles
  const styles = useMemo(() => StyleSheet.create({
    container: {
      padding: spacing.md,
    },
    title: {
      fontSize: typography.sizes.lg,
      fontWeight: typography.weights.semibold,
      color: colors.text,
      marginBottom: spacing.sm,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: spacing.lg,
      lineHeight: 20,
    },
    uploadOptionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacing.md,
    },
    disabledOverlay: {
      opacity: 0.5,
    },
  }), [colors, spacing, typography]);

  // Check if upload is ready
  const isUploadReady = selectedDocumentType && selectedDocumentCategory && !disabled && !currentUploadId;
  const isUploading = !!currentUploadId;

  return (
    <View style={[styles.container, disabled && styles.disabledOverlay]}>
      <Text style={styles.title}>Upload Document</Text>
      <Text style={styles.subtitle}>
        Select a document type and choose how you'd like to upload your file
      </Text>

      <DocumentTypeSelector
        onDocumentTypeSelect={handleDocumentTypeSelect}
        preselectedDocumentType={preselectedDocumentType}
        disabled={disabled || isUploading}
      />

      {selectedDocumentType && selectedDocumentCategory && (
        <View style={styles.uploadOptionsContainer}>
          <UploadOption
            icon={<Camera size={24} color={isUploadReady ? colors.primary[500] : colors.gray[400]} />}
            text="Camera"
            onPress={handleCameraUpload}
            disabled={!isUploadReady}
            colors={colors}
          />
          
          <UploadOption
            icon={<ImageIcon size={24} color={isUploadReady ? colors.primary[500] : colors.gray[400]} />}
            text="Gallery"
            onPress={handleGalleryUpload}
            disabled={!isUploadReady}
            colors={colors}
          />
          
          <UploadOption
            icon={<File size={24} color={isUploadReady ? colors.primary[500] : colors.gray[400]} />}
            text="Files"
            onPress={handleDocumentUpload}
            disabled={!isUploadReady}
            colors={colors}
          />
        </View>
      )}

      {uploadProgress && (
        <ProgressIndicator progress={uploadProgress} colors={colors} />
      )}
    </View>
  );
});

OptimizedDocumentUploader.displayName = 'OptimizedDocumentUploader';

export default OptimizedDocumentUploader;
