import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, FileText, AlertCircle, Upload, FileCheck, FileBarChart } from 'lucide-react-native';
import { router, useFocusEffect } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import DocumentVerificationList from '@/components/documents/DocumentVerificationList';
import DocumentViewer from '@/components/documents/DocumentViewer';
import { Document } from '@/components/documents/types';
import TabNavigation from '@/components/navigation/TabNavigation';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { useVerifiedDocuments } from '@/context/OptimizedUploadContext';
import { showToast } from '@/utils/toast';

export default function VerificationScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Get documents from context
  const {
    pendingDocuments,
    verifiedDocuments,
    documentsInVerification
  } = useDocumentUpload();

  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [viewerVisible, setViewerVisible] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0);

  // Use focus effect to refresh documents when the screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log('[VerificationScreen] Screen focused, refreshing documents');
      // Force a re-render when the screen is focused
      setForceUpdate(prev => prev + 1);

      return () => {
        // Cleanup when screen is unfocused
        console.log('[VerificationScreen] Screen unfocused');
      };
    }, [])
  );

  // Combine all documents for display
  const documents = [...pendingDocuments, ...verifiedDocuments];

  // Log document status for debugging
  console.log('[VerificationScreen] Pending documents:', pendingDocuments.length);
  console.log('[VerificationScreen] Verified documents:', verifiedDocuments.length);
  console.log('[VerificationScreen] Documents in verification:', documentsInVerification.length);
  console.log('[VerificationScreen] Force update count:', forceUpdate);

  console.log('[VerificationScreen] Rendering with', documents.length, 'documents');

  // Define tabs for navigation
  const tabs = [
    {
      id: 'upload',
      title: 'Upload Hub',
      icon: <Upload size={16} color={colors.textSecondary} />
    },
    {
      id: 'verification',
      title: 'Verification Status',
      icon: <FileCheck size={16} color={colors.primary[500]} />
    },
    {
      id: 'policies',
      title: 'Policy Repository',
      icon: <FileBarChart size={16} color={colors.textSecondary} />
    }
  ];

  // Handle tab change
  const handleTabChange = useCallback((tabId: string) => {
    if (tabId === 'upload') {
      router.push('/documents');
    } else if (tabId === 'verification') {
      // Stay on current page
      return;
    } else if (tabId === 'policies') {
      router.push('/documents/policies');
    }
  }, []);

  // Handle document view
  const handleViewDocument = (document: Document) => {
    console.log('[VerificationScreen] Viewing document:', document);
    setSelectedDocument(document);
    setViewerVisible(true);
  };

  // Handle document re-upload
  const handleReuploadDocument = (document: Document) => {
    console.log('[VerificationScreen] Re-uploading document:', document);

    // Navigate to the upload screen with the document type pre-selected
    router.push({
      pathname: '/documents',
      params: {
        reupload: 'true',
        documentType: document.type,
        documentName: document.name
      }
    });

    // Show toast notification
    showToast(
      'info',
      'Re-upload Document',
      `Please upload a new version of ${document.name}`,
      { visibilityTime: 3000 }
    );
  };

  // Group documents by status
  const pendingDocs = documents.filter(doc => doc.status === 'pending');
  const verifiedDocs = documents.filter(doc => doc.status === 'verified');
  const rejectedDocs = documents.filter(doc => doc.status === 'rejected');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    scrollView: {
      flex: 1,
      marginBottom: Platform.OS === 'ios' ? 88 : 60, // Add margin for bottom nav bar
    },
    content: {
      padding: spacing.lg,
    },
    infoCard: {
      backgroundColor: `${colors.primary[500]}10`,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
    },
    infoIcon: {
      marginRight: spacing.md,
    },
    infoText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
      flex: 1,
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginTop: spacing.md,
      marginBottom: spacing.sm,
    },
    sectionSubtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    emptyStateContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    emptyStateIcon: {
      marginBottom: spacing.md,
    },
    emptyStateText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Verification Status</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <TabNavigation
            tabs={tabs}
            activeTab="verification"
            onTabChange={handleTabChange}
            scrollable={true}
            style={{ marginBottom: spacing.md }}
          />
          <Animated.View
            style={styles.infoCard}
            entering={FadeInDown.delay(100).springify()}
          >
            <AlertCircle
              size={24}
              color={colors.primary[500]}
              style={styles.infoIcon}
            />
            <Text style={styles.infoText}>
              Track the verification status of your submitted documents.
              Rejected documents can be re-uploaded.
            </Text>
          </Animated.View>

          {documents.length === 0 ? (
            <Animated.View
              style={styles.emptyStateContainer}
              entering={FadeInDown.delay(200).springify()}
            >
              <FileText
                size={48}
                color={colors.textSecondary}
                style={styles.emptyStateIcon}
              />
              <Text style={styles.emptyStateText}>
                You haven't submitted any documents for verification yet
              </Text>
            </Animated.View>
          ) : (
            <>
              {pendingDocs.length > 0 && (
                <Animated.View entering={FadeInDown.delay(200).springify()}>
                  <Text style={styles.sectionTitle}>
                    Pending Verification
                    {documentsInVerification.length > 0 && (
                      <Text style={styles.sectionSubtitle}>
                        {' '}• {documentsInVerification.length} in progress
                      </Text>
                    )}
                  </Text>
                  <DocumentVerificationList
                    documents={pendingDocs}
                    onViewDocument={handleViewDocument}
                    emptyStateMessage="No documents pending verification"
                    showVerificationProgress={true}
                  />
                </Animated.View>
              )}

              {rejectedDocs.length > 0 && (
                <Animated.View entering={FadeInDown.delay(300).springify()}>
                  <Text style={styles.sectionTitle}>
                    Rejected Documents
                    <Text style={styles.sectionSubtitle}>
                      {' '}• {rejectedDocs.length} {rejectedDocs.length === 1 ? 'document' : 'documents'}
                    </Text>
                  </Text>
                  <DocumentVerificationList
                    documents={rejectedDocs}
                    onViewDocument={handleViewDocument}
                    onReuploadDocument={handleReuploadDocument}
                    emptyStateMessage="No rejected documents"
                    showVerificationProgress={false}
                  />
                </Animated.View>
              )}

              {verifiedDocs.length > 0 && (
                <Animated.View entering={FadeInDown.delay(400).springify()}>
                  <Text style={styles.sectionTitle}>
                    Verified Documents
                    <Text style={styles.sectionSubtitle}>
                      {' '}• {verifiedDocs.length} {verifiedDocs.length === 1 ? 'document' : 'documents'}
                    </Text>
                  </Text>
                  <DocumentVerificationList
                    documents={verifiedDocs}
                    onViewDocument={handleViewDocument}
                    emptyStateMessage="No verified documents"
                    showVerificationProgress={false}
                  />
                </Animated.View>
              )}
            </>
          )}
        </View>
      </ScrollView>

      {selectedDocument && (
        <DocumentViewer
          document={selectedDocument}
          visible={viewerVisible}
          onClose={() => setViewerVisible(false)}
        />
      )}

      {/* Bottom Navigation Bar */}
      <BottomNavBar />
    </SafeAreaView>
  );
}
