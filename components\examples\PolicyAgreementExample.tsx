import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import PolicyAgreementComponent from '../PolicyAgreementComponent';
import { Colors } from '@/constants/Colors';

/**
 * Example component showing how to use PolicyAgreementComponent
 * This demonstrates integration in quote forms, application forms, etc.
 */
export default function PolicyAgreementExample() {
  const [isAgreed, setIsAgreed] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAgreementChange = (agreed: boolean) => {
    setIsAgreed(agreed);
    console.log('Policy agreement changed:', agreed);
  };

  const handleSubmit = async () => {
    if (!isAgreed) {
      Alert.alert(
        'Agreement Required',
        'You must read and agree to the declaration before submitting your application.'
      );
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Success',
        'Your application has been submitted successfully!',
        [{ text: 'OK', onPress: () => console.log('Application submitted') }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit application. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>Insurance Application Form</Text>
        <Text style={styles.subtitle}>
          Complete your application by reviewing and accepting the terms below.
        </Text>

        {/* Example form fields would go here */}
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Application Details</Text>
          <Text style={styles.placeholder}>
            [Other form fields would be here - personal info, coverage details, etc.]
          </Text>
        </View>

        {/* Policy Agreement Component */}
        <PolicyAgreementComponent
          onAgreementChange={handleAgreementChange}
          isRequired={true}
          disabled={isSubmitting}
          style={styles.policyAgreement}
        />

        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            (!isAgreed || isSubmitting) && styles.submitButtonDisabled,
          ]}
          onPress={handleSubmit}
          disabled={!isAgreed || isSubmitting}
        >
          <Text style={styles.submitButtonText}>
            {isSubmitting ? 'Submitting...' : 'Submit Application'}
          </Text>
        </TouchableOpacity>

        {/* Usage Instructions */}
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>Usage Instructions:</Text>
          <Text style={styles.instructionsText}>
            1. Import PolicyAgreementComponent into your form{'\n'}
            2. Add onAgreementChange callback to track agreement state{'\n'}
            3. Disable form submission until user agrees{'\n'}
            4. Component handles scroll-to-read requirement automatically{'\n'}
            5. Includes accessibility support and proper error handling
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollContent: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  formSection: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  placeholder: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 20,
  },
  policyAgreement: {
    marginBottom: 24,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonDisabled: {
    backgroundColor: Colors.disabled,
  },
  submitButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  instructionsContainer: {
    backgroundColor: Colors.inputBackground,
    borderRadius: 8,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
  },
});
