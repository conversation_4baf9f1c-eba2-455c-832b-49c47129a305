import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface PulaIconProps {
  size?: number;
  color?: string;
  style?: any;
}

/**
 * Custom Pula (P) icon component for Botswana currency
 * Since there's no standard Pula icon in icon libraries, we create a custom one
 */
const PulaIcon: React.FC<PulaIconProps> = ({ 
  size = 20, 
  color = '#000000',
  style 
}) => {
  const styles = StyleSheet.create({
    container: {
      width: size,
      height: size,
      justifyContent: 'center',
      alignItems: 'center',
      ...style
    },
    text: {
      fontSize: size * 0.8, // Make the P slightly smaller than the container
      fontWeight: 'bold',
      color: color,
      fontFamily: 'System', // Use system font for consistency
    }
  });

  return (
    <View style={styles.container}>
      <Text style={styles.text}>P</Text>
    </View>
  );
};

export default PulaIcon;
