# API Integration Guide - Inerca Holdings Mobile App

## Overview

This document provides comprehensive information about the backend API integration for the Inerca Holdings mobile application.

## 🔗 **Backend Information**

### **Base URLs**
- **Production**: `https://inerca-backend-wild-leaf-8326.fly.dev/`
- **API Documentation**: `https://inerca-backend-wild-leaf-8326.fly.dev/docs/`
- **OpenAPI Specification**: `https://inerca-backend-wild-leaf-8326.fly.dev/openapi.json`

### **Environment Configuration**
```typescript
// Environment variables
const BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL || 'https://inerca-backend-wild-leaf-8326.fly.dev/';
```

## 🔐 **Authentication**

### **Authentication Flow**
1. **Login**: POST `/api/v1/login` with form data
2. **Register**: POST `/api/v1/register` with JSON data
3. **Token Storage**: Store `access_token` in AsyncStorage
4. **Authorization**: Include `Bearer {token}` in headers

### **Login Implementation**
```typescript
// Login API call
const login = async (email: string, password: string) => {
  const response = await fetch(`${BASE_URL}/api/v1/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      username: email,
      password: password,
    }),
  });

  if (!response.ok) {
    throw new Error('Login failed');
  }

  return await response.json(); // Returns { access_token, token_type }
};
```

### **Registration Implementation**
```typescript
// Registration API call
const register = async (userData: UserCreate) => {
  const response = await fetch(`${BASE_URL}/api/v1/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData),
  });

  if (!response.ok) {
    throw new Error('Registration failed');
  }

  return await response.json(); // Returns UserPublic
};
```

### **Google OAuth Integration**
```typescript
// Google OAuth flow
const googleLogin = async () => {
  // Open OAuth session
  const result = await WebBrowser.openAuthSessionAsync(
    'https://inerca-backend-wild-leaf-8326.fly.dev/api/v1/google/login',
    'https://inerca-backend-wild-leaf-8326.fly.dev/api/v1/google/callback'
  );

  // Process callback with access token
  if (result.type === 'success' && result.url.includes('access_token')) {
    const url = new URL(result.url);
    const accessToken = url.searchParams.get('access_token');
    
    // Store token and authenticate user
    await AsyncStorage.setItem('token', accessToken);
    return await getCurrentUser();
  }
};
```

## 🔑 **Password Reset**

### **Forgot Password Flow**
```typescript
// Step 1: Request reset code
const forgotPassword = async (email: string) => {
  const response = await fetch(`${BASE_URL}/api/v1/forgot-password?email=${encodeURIComponent(email)}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to send reset code');
  }
};

// Step 2: Reset password with code
const resetPassword = async (email: string, code: number, newPassword: string) => {
  const response = await fetch(`${BASE_URL}/api/v1/reset-password`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email,
      code,
      new_password: newPassword,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to reset password');
  }
};
```

## 👤 **User Management**

### **Get Current User**
```typescript
const getCurrentUser = async () => {
  const token = await AsyncStorage.getItem('token');
  if (!token) {
    throw new Error('No authentication token found');
  }

  const response = await fetch(`${BASE_URL}/api/v1/user/me`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to get user data');
  }

  return await response.json(); // Returns UserPublic
};
```

### **Update User Profile**
```typescript
const updateUser = async (userData: UserUpdate) => {
  const token = await AsyncStorage.getItem('token');
  
  const response = await fetch(`${BASE_URL}/api/v1/user/me`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData),
  });

  if (!response.ok) {
    throw new Error('Failed to update user');
  }

  return await response.json();
};
```

## 📄 **Data Types**

### **User Types**
```typescript
interface UserCreate {
  email: string;
  hashed_password: string;
  username: string;
  phone_number: string;
  role: 'user' | 'admin';
}

interface UserPublic {
  id: string;
  email: string;
  username: string;
  phone_number: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface UserUpdate {
  username?: string;
  phone_number?: string;
  email?: string;
}
```

### **Authentication Types**
```typescript
interface Token {
  access_token: string;
  token_type: string;
  expires_in?: number;
}

interface UserPasswordReset {
  email: string;
  code: number;
  new_password: string;
}
```

## 🛡️ **Error Handling**

### **Standard Error Response**
```typescript
interface APIError {
  detail: string;
  status_code?: number;
}
```

### **Error Handling Implementation**
```typescript
const handleAPIError = async (response: Response) => {
  if (!response.ok) {
    let errorMessage = `Request failed: ${response.status}`;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.detail || errorMessage;
    } catch {
      // If JSON parsing fails, use default message
    }
    
    throw new Error(errorMessage);
  }
  
  return response;
};

// Usage in API calls
const apiCall = async () => {
  try {
    const response = await fetch(url, options);
    await handleAPIError(response);
    return await response.json();
  } catch (error) {
    console.error('API Error:', error.message);
    throw error;
  }
};
```

## 🔄 **Request/Response Interceptors**

### **Axios Configuration** (if using Axios)
```typescript
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
});

// Request interceptor for auth token
api.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to login
      AsyncStorage.removeItem('token');
      // Navigate to login screen
    }
    return Promise.reject(error);
  }
);
```

## 📊 **API Service Structure**

### **Service Organization**
```typescript
export const apiService = {
  auth: {
    login,
    register,
    getCurrentUser,
    forgotPassword,
    resetPassword,
    googleCallback,
  },
  user: {
    getProfile,
    updateProfile,
    uploadProfileImage,
  },
  insurance: {
    getQuotes,
    createQuote,
    getApplications,
    submitApplication,
  },
  documents: {
    uploadDocument,
    getDocuments,
    verifyDocument,
  },
  policies: {
    getPolicies,
    getPolicyDetails,
    renewPolicy,
  },
  claims: {
    getClaims,
    submitClaim,
    getClaimDetails,
  },
};
```

## 🔍 **Debugging & Logging**

### **API Logging Implementation**
```typescript
const logAPICall = (method: string, url: string, data?: any) => {
  if (__DEV__) {
    console.log(`[API] ${method} ${url}`);
    if (data) {
      console.log('[API] Request data:', data);
    }
  }
};

const logAPIResponse = (url: string, response: any) => {
  if (__DEV__) {
    console.log(`[API] Response from ${url}:`, response);
  }
};

// Usage in API calls
const apiCallWithLogging = async (method: string, url: string, data?: any) => {
  logAPICall(method, url, data);
  
  try {
    const response = await fetch(url, { method, body: data });
    const result = await response.json();
    
    logAPIResponse(url, result);
    return result;
  } catch (error) {
    console.error(`[API] Error in ${method} ${url}:`, error);
    throw error;
  }
};
```

## 🚀 **Performance Optimization**

### **Request Caching**
```typescript
const cache = new Map();

const cachedRequest = async (url: string, options: RequestInit, cacheTime = 5 * 60 * 1000) => {
  const cacheKey = `${url}-${JSON.stringify(options)}`;
  const cached = cache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < cacheTime) {
    return cached.data;
  }
  
  const response = await fetch(url, options);
  const data = await response.json();
  
  cache.set(cacheKey, {
    data,
    timestamp: Date.now(),
  });
  
  return data;
};
```

### **Request Retry Logic**
```typescript
const retryRequest = async (fn: () => Promise<any>, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }
};
```

## 📋 **Best Practices**

### **Security**
- Always use HTTPS for API calls
- Store tokens securely in AsyncStorage
- Implement token refresh logic
- Validate all user inputs
- Handle sensitive data appropriately

### **Performance**
- Implement request caching where appropriate
- Use request timeouts
- Implement retry logic for failed requests
- Minimize payload sizes
- Use pagination for large datasets

### **Error Handling**
- Provide meaningful error messages
- Implement proper error boundaries
- Log errors for debugging
- Handle network failures gracefully
- Implement offline support where possible

## 🔧 **Environment Variables**

```env
# Required environment variables
EXPO_PUBLIC_API_BASE_URL=https://inerca-backend-wild-leaf-8326.fly.dev/
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_API_TIMEOUT=10000
EXPO_PUBLIC_ENABLE_API_LOGGING=true
```

## 📞 **Support & Troubleshooting**

### **Common Issues**
1. **401 Unauthorized**: Check token validity and refresh if needed
2. **Network Errors**: Implement retry logic and offline handling
3. **Timeout Errors**: Increase timeout or optimize requests
4. **CORS Issues**: Ensure backend CORS configuration is correct

### **Debug Tools**
- React Native Debugger for network inspection
- Flipper for advanced debugging
- Console logging for API calls
- Error tracking for production issues
