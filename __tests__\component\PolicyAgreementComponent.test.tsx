import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import PolicyAgreementComponent from '../../components/PolicyAgreementComponent';

describe('PolicyAgreementComponent', () => {
  const mockOnAgreementChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with correct initial state', () => {
    const { getByText, getByRole } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    expect(getByText('Legal Declaration')).toBeTruthy();
    expect(getByText('Declaration and Terms of Submission')).toBeTruthy();
    expect(getByText('Please scroll to read the complete declaration')).toBeTruthy();
    
    const checkbox = getByRole('checkbox');
    expect(checkbox.props.accessibilityState.checked).toBe(false);
  });

  it('should display required indicator when isRequired is true', () => {
    const { getByText } = render(
      <PolicyAgreementComponent 
        onAgreementChange={mockOnAgreementChange} 
        isRequired={true}
      />
    );

    // Should have required asterisks
    const requiredIndicators = getByText('*');
    expect(requiredIndicators).toBeTruthy();
  });

  it('should not display required indicator when isRequired is false', () => {
    const { queryByText } = render(
      <PolicyAgreementComponent 
        onAgreementChange={mockOnAgreementChange} 
        isRequired={false}
      />
    );

    // Should not have required asterisks in header
    expect(queryByText('*')).toBeFalsy();
  });

  it('should display complete legal declaration text', () => {
    const { getByText } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    // Check for key parts of the legal text
    expect(getByText(/I hereby declare that the information provided/)).toBeTruthy();
    expect(getByText(/This form, once submitted, constitutes a legal and binding declaration/)).toBeTruthy();
    expect(getByText(/I consent to the insurance company/)).toBeTruthy();
    expect(getByText(/By submitting this form, I confirm/)).toBeTruthy();
  });

  it('should enable checkbox after scrolling to bottom', async () => {
    const { getByRole, getByTestId } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    const scrollView = getByTestId('declaration-scroll-view');
    const checkbox = getByRole('checkbox');

    // Initially checkbox should be disabled
    expect(checkbox.props.accessibilityState.checked).toBe(false);

    // Simulate scrolling to bottom
    fireEvent.scroll(scrollView, {
      nativeEvent: {
        contentOffset: { y: 1000 },
        contentSize: { height: 1000 },
        layoutMeasurement: { height: 200 },
      },
    });

    await waitFor(() => {
      // Checkbox should now be enabled (we can't directly test enabled state, 
      // but we can test that it responds to press)
      fireEvent.press(checkbox);
      expect(mockOnAgreementChange).toHaveBeenCalledWith(true);
    });
  });

  it('should call onAgreementChange when checkbox is toggled', async () => {
    const { getByRole, getByTestId } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    const scrollView = getByTestId('declaration-scroll-view');
    const checkbox = getByRole('checkbox');

    // First scroll to enable checkbox
    fireEvent.scroll(scrollView, {
      nativeEvent: {
        contentOffset: { y: 1000 },
        contentSize: { height: 1000 },
        layoutMeasurement: { height: 200 },
      },
    });

    await waitFor(() => {
      // Toggle checkbox on
      fireEvent.press(checkbox);
      expect(mockOnAgreementChange).toHaveBeenCalledWith(true);
    });

    // Toggle checkbox off
    fireEvent.press(checkbox);
    expect(mockOnAgreementChange).toHaveBeenCalledWith(false);
  });

  it('should not allow checkbox interaction when disabled', () => {
    const { getByRole } = render(
      <PolicyAgreementComponent 
        onAgreementChange={mockOnAgreementChange} 
        disabled={true}
      />
    );

    const checkbox = getByRole('checkbox');

    // Try to press checkbox when component is disabled
    fireEvent.press(checkbox);

    // Should not call onAgreementChange
    expect(mockOnAgreementChange).not.toHaveBeenCalled();
  });

  it('should hide scroll indicator after scrolling to bottom', async () => {
    const { getByText, queryByText, getByTestId } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    const scrollView = getByTestId('declaration-scroll-view');

    // Initially should show scroll indicator
    expect(getByText('Please scroll to read the complete declaration')).toBeTruthy();

    // Simulate scrolling to bottom
    fireEvent.scroll(scrollView, {
      nativeEvent: {
        contentOffset: { y: 1000 },
        contentSize: { height: 1000 },
        layoutMeasurement: { height: 200 },
      },
    });

    await waitFor(() => {
      // Scroll indicator should be hidden
      expect(queryByText('Please scroll to read the complete declaration')).toBeFalsy();
    });
  });

  it('should show help text when checkbox is not yet enabled', () => {
    const { getByText } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    expect(getByText('You must read the complete declaration before you can agree to the terms.')).toBeTruthy();
  });

  it('should hide help text after scrolling to bottom', async () => {
    const { queryByText, getByTestId } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    const scrollView = getByTestId('declaration-scroll-view');

    // Simulate scrolling to bottom
    fireEvent.scroll(scrollView, {
      nativeEvent: {
        contentOffset: { y: 1000 },
        contentSize: { height: 1000 },
        layoutMeasurement: { height: 200 },
      },
    });

    await waitFor(() => {
      // Help text should be hidden
      expect(queryByText('You must read the complete declaration before you can agree to the terms.')).toBeFalsy();
    });
  });

  it('should apply custom styles when provided', () => {
    const customStyle = { backgroundColor: 'red', margin: 20 };
    
    const { getByTestId } = render(
      <PolicyAgreementComponent 
        onAgreementChange={mockOnAgreementChange} 
        style={customStyle}
      />
    );

    const container = getByTestId('policy-agreement-container');
    expect(container.props.style).toEqual(expect.arrayContaining([
      expect.objectContaining(customStyle)
    ]));
  });

  it('should have proper accessibility labels', () => {
    const { getByRole } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    const checkbox = getByRole('checkbox');
    expect(checkbox.props.accessibilityLabel).toBe('I agree to the declaration and terms of submission');
  });

  it('should handle scroll events correctly', async () => {
    const { getByTestId } = render(
      <PolicyAgreementComponent onAgreementChange={mockOnAgreementChange} />
    );

    const scrollView = getByTestId('declaration-scroll-view');

    // Test scroll event that doesn't reach bottom
    fireEvent.scroll(scrollView, {
      nativeEvent: {
        contentOffset: { y: 100 },
        contentSize: { height: 1000 },
        layoutMeasurement: { height: 200 },
      },
    });

    // Should still show scroll indicator
    await waitFor(() => {
      expect(getByTestId('scroll-indicator')).toBeTruthy();
    });

    // Test scroll event that reaches bottom
    fireEvent.scroll(scrollView, {
      nativeEvent: {
        contentOffset: { y: 800 },
        contentSize: { height: 1000 },
        layoutMeasurement: { height: 200 },
      },
    });

    // Should hide scroll indicator
    await waitFor(() => {
      expect(() => getByTestId('scroll-indicator')).toThrow();
    });
  });
});
