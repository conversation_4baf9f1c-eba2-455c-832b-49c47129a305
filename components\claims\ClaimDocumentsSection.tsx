import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Claim, ClaimDocument } from '@/types/claim.types';
import { FileText, Upload, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react-native';
import UltimateDocumentUploader from '@/components/documents/UltimateDocumentUploader';

interface ClaimDocumentsSectionProps {
  claim: Claim;
  onUploadDocument: (document: any) => void;
  onViewDocument: (document: ClaimDocument) => void;
}

const ClaimDocumentsSection: React.FC<ClaimDocumentsSectionProps> = ({
  claim,
  onUploadDocument,
  onViewDocument
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const styles = StyleSheet.create({
    container: {
      marginVertical: spacing.md,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    documentsContainer: {
      marginBottom: spacing.md,
    },
    documentItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.sm,
    },
    documentIcon: {
      marginRight: spacing.md,
    },
    documentInfo: {
      flex: 1,
    },
    documentName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.xs / 2,
    },
    documentType: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.xs / 2,
    },
    documentDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.neutral[100],
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      borderRadius: 100,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginLeft: spacing.xs / 2,
    },
    requiredBadge: {
      backgroundColor: colors.warning[100],
      paddingHorizontal: spacing.xs,
      paddingVertical: spacing.xs / 4,
      borderRadius: borders.radius.sm,
      marginLeft: spacing.xs,
    },
    requiredText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.warning[700],
    },
    uploadSection: {
      marginTop: spacing.md,
    },
    uploadTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    noDocumentsText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginVertical: spacing.lg,
    },
  });

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status icon and color
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          icon: <CheckCircle size={14} color={colors.success[500]} />,
          text: 'Verified',
          textColor: colors.success[500],
          backgroundColor: colors.success[100],
        };
      case 'rejected':
        return {
          icon: <XCircle size={14} color={colors.error[500]} />,
          text: 'Rejected',
          textColor: colors.error[500],
          backgroundColor: colors.error[100],
        };
      case 'pending':
      default:
        return {
          icon: <Clock size={14} color={colors.warning[500]} />,
          text: 'Pending Verification',
          textColor: colors.warning[500],
          backgroundColor: colors.warning[100],
        };
    }
  };

  // Group documents by status
  const groupedDocuments = {
    pending: claim.documents.filter(doc => doc.status === 'pending'),
    verified: claim.documents.filter(doc => doc.status === 'verified'),
    rejected: claim.documents.filter(doc => doc.status === 'rejected'),
  };

  // Check if all required documents are uploaded and verified
  const requiredDocuments = claim.documents.filter(doc => doc.required);
  const allRequiredDocumentsUploaded = requiredDocuments.length > 0 &&
    requiredDocuments.every(doc => doc.status === 'verified');

  // Render document item
  const renderDocumentItem = (document: ClaimDocument, index: number) => {
    const statusInfo = getStatusInfo(document.status);

    return (
      <TouchableOpacity
        key={document.id}
        style={styles.documentItem}
        onPress={() => onViewDocument(document)}
      >
        <View style={styles.documentIcon}>
          <FileText size={24} color={colors.primary[500]} />
        </View>

        <View style={styles.documentInfo}>
          <Text style={styles.documentName}>
            {document.name}
            {document.required && (
              <View style={styles.requiredBadge}>
                <Text style={styles.requiredText}>Required</Text>
              </View>
            )}
          </Text>
          <Text style={styles.documentType}>{document.type}</Text>
          <Text style={styles.documentDate}>Uploaded: {formatDate(document.date)}</Text>
          {document.verificationDate && (
            <Text style={styles.documentDate}>
              Verified: {formatDate(document.verificationDate)}
            </Text>
          )}
        </View>

        <View style={[
          styles.statusContainer,
          { backgroundColor: statusInfo.backgroundColor }
        ]}>
          {statusInfo.icon}
          <Text style={[
            styles.statusText,
            { color: statusInfo.textColor }
          ]}>
            {statusInfo.text}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Documents</Text>

      <View style={styles.documentsContainer}>
        {claim.documents.length === 0 ? (
          <Text style={styles.noDocumentsText}>
            No documents uploaded yet
          </Text>
        ) : (
          <>
            {groupedDocuments.pending.length > 0 && (
              <>
                <Text style={styles.uploadTitle}>Pending Verification</Text>
                {groupedDocuments.pending.map((doc, index) => renderDocumentItem(doc, index))}
              </>
            )}

            {groupedDocuments.verified.length > 0 && (
              <>
                <Text style={styles.uploadTitle}>Verified Documents</Text>
                {groupedDocuments.verified.map((doc, index) => renderDocumentItem(doc, index))}
              </>
            )}

            {groupedDocuments.rejected.length > 0 && (
              <>
                <Text style={styles.uploadTitle}>Rejected Documents</Text>
                {groupedDocuments.rejected.map((doc, index) => renderDocumentItem(doc, index))}
              </>
            )}
          </>
        )}
      </View>

      <View style={styles.uploadSection}>
        <Text style={styles.uploadTitle}>Upload Document</Text>
        <UltimateDocumentUploader
          onDocumentUploaded={onUploadDocument}
          preselectedDocumentType="claim"
          maxFileSize={10 * 1024 * 1024}
          allowedFileTypes={['image', 'pdf', 'doc']}
          uploadType="document"
        />
      </View>
    </View>
  );
};

export default ClaimDocumentsSection;
