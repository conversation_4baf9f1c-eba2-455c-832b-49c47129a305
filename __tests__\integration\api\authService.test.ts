import { apiService } from '../../../services/api';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock fetch globally
global.fetch = jest.fn();

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage');

describe('Auth API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  describe('login', () => {
    it('should successfully login with valid credentials', async () => {
      const mockResponse = {
        access_token: 'mock-access-token',
        token_type: 'bearer',
        expires_in: 3600,
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockResponse,
      });

      const result = await apiService.auth.login('<EMAIL>', 'password123');

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/login'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: expect.stringContaining('username=test%40example.com&password=password123'),
        })
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle login failure with invalid credentials', async () => {
      const mockErrorResponse = {
        detail: 'Invalid credentials',
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => mockErrorResponse,
      });

      await expect(
        apiService.auth.login('<EMAIL>', 'wrongpassword')
      ).rejects.toThrow('Invalid credentials');

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/login'),
        expect.objectContaining({
          method: 'POST',
        })
      );
    });

    it('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(
        apiService.auth.login('<EMAIL>', 'password123')
      ).rejects.toThrow('Network error');
    });
  });

  describe('register', () => {
    it('should successfully register a new user', async () => {
      const mockUserData = {
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        username: 'New User',
        phone_number: '+2671234567',
        role: 'user' as const,
      };

      const mockResponse = {
        id: '123',
        email: '<EMAIL>',
        username: 'New User',
        phone_number: '+2671234567',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockResponse,
      });

      const result = await apiService.auth.register(mockUserData);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/register'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(mockUserData),
        })
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle registration failure with existing email', async () => {
      const mockUserData = {
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        username: 'Existing User',
        phone_number: '+2671234567',
        role: 'user' as const,
      };

      const mockErrorResponse = {
        detail: 'Email already registered',
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => mockErrorResponse,
      });

      await expect(
        apiService.auth.register(mockUserData)
      ).rejects.toThrow('Email already registered');
    });
  });

  describe('getCurrentUser', () => {
    it('should successfully get current user with valid token', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        username: 'Test User',
        phone_number: '+2671234567',
        is_active: true,
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce('valid-token');

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockUser,
      });

      const result = await apiService.auth.getCurrentUser();

      expect(AsyncStorage.getItem).toHaveBeenCalledWith('token');
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/user/me'),
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Authorization': 'Bearer valid-token',
            'Content-Type': 'application/json',
          },
        })
      );

      expect(result).toEqual(mockUser);
    });

    it('should throw error when no token is available', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(null);

      await expect(
        apiService.auth.getCurrentUser()
      ).rejects.toThrow('No authentication token found');

      expect(fetch).not.toHaveBeenCalled();
    });

    it('should handle unauthorized response', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce('invalid-token');

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ detail: 'Token expired' }),
      });

      await expect(
        apiService.auth.getCurrentUser()
      ).rejects.toThrow('Token expired');
    });
  });

  describe('forgotPassword', () => {
    it('should successfully send forgot password request', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({}),
      });

      await apiService.auth.forgotPassword('<EMAIL>');

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/forgot-password?email=test%40example.com'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );
    });

    it('should handle forgot password failure', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ detail: 'Email not found' }),
      });

      await expect(
        apiService.auth.forgotPassword('<EMAIL>')
      ).rejects.toThrow('Email not found');
    });
  });

  describe('resetPassword', () => {
    it('should successfully reset password', async () => {
      const resetData = {
        email: '<EMAIL>',
        code: 123456,
        new_password: 'newpassword123',
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({}),
      });

      await apiService.auth.resetPassword(resetData);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/reset-password'),
        expect.objectContaining({
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(resetData),
        })
      );
    });

    it('should handle invalid reset code', async () => {
      const resetData = {
        email: '<EMAIL>',
        code: 999999,
        new_password: 'newpassword123',
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ detail: 'Invalid or expired code' }),
      });

      await expect(
        apiService.auth.resetPassword(resetData)
      ).rejects.toThrow('Invalid or expired code');
    });
  });

  describe('API error handling', () => {
    it('should handle JSON parsing errors', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => {
          throw new Error('Invalid JSON');
        },
      });

      await expect(
        apiService.auth.login('<EMAIL>', 'password')
      ).rejects.toThrow('Login failed: 500');
    });

    it('should handle network timeouts', async () => {
      (fetch as jest.Mock).mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      await expect(
        apiService.auth.login('<EMAIL>', 'password')
      ).rejects.toThrow('Request timeout');
    });
  });
});
