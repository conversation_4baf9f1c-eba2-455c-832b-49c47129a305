{"name": "inerca-holdings-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "test": "jest", "test:unit": "jest --testPathPattern=__tests__/unit", "test:integration": "jest --testPathPattern=__tests__/integration", "test:component": "jest --testPathPattern=__tests__/component", "test:e2e": "jest --testPathPattern=__tests__/e2e", "test:coverage": "jest --coverage", "test:all": "jest --coverage --verbose", "test:watch": "jest --watch", "test:ci": "jest --coverage --watchAll=false --ci"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/cli": "^20.0.1", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.0.0", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.8.1", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo": "53.0.20", "expo-auth-session": "~6.2.1", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-checkbox": "~4.1.4", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.3", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.6", "expo-file-system": "^18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "^16.1.4", "expo-intent-launcher": "~12.1.5", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.4", "expo-local-authentication": "~16.0.5", "expo-notifications": "~0.31.4", "expo-print": "^14.1.4", "expo-router": "~5.1.4", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-otp-inputs": "^7.4.0", "react-native-pdf": "^6.7.7", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^3.5.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.0", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.3", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-native-zoomable-view": "^0.3.1", "react-redux": "^9.2.0", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-class-properties": "^7.24.1", "@testing-library/react-native": "^12.4.3", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "axios-mock-adapter": "^1.22.0", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "jest": "^29.7.0", "jest-expo": "~53.0.9", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "setupFiles": ["<rootDir>/jest.setup.js"], "collectCoverageFrom": ["store/**/*.{ts,tsx}", "services/**/*.{ts,tsx}", "utils/**/*.{ts,tsx}", "components/**/*.{ts,tsx}", "hooks/**/*.{ts,tsx}", "!**/*.d.ts", "!**/node_modules/**", "!**/__tests__/**", "!**/coverage/**"], "coverageReporters": ["html", "json", "lcov", "text", "text-summary"], "coverageDirectory": "coverage", "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}}