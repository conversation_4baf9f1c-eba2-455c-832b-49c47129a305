/**
 * UploadManager.ts
 *
 * A comprehensive upload queue system that handles uploads outside of React's rendering cycle.
 * This prevents app rerenders and provides a stable upload experience with proper memory management.
 */

import { Document, DocumentCategory, DocumentFileType } from '@/components/documents/types';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import Toast from 'react-native-toast-message';
import { AppState, AppStateStatus } from 'react-native';

// Upload states
export type UploadState = 'idle' | 'preparing' | 'selecting' | 'processing' | 'uploading' | 'success' | 'error';

// Upload result
export interface UploadResult {
  success: boolean;
  document?: Document;
  error?: string;
  uploadId?: string;
}

// Upload queue item
export interface UploadQueueItem {
  id: string;
  type: 'image' | 'document';
  useCamera?: boolean;
  documentType: string;
  documentCategory: DocumentCategory;
  callback: (result: UploadResult) => void;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

// Upload progress tracking
export interface UploadProgress {
  uploadId: string;
  state: UploadState;
  progress: number;
  error?: string;
}

// Upload manager class with queue system
class UploadManagerClass {
  // Private state
  private _isUploading: boolean = false;
  private _currentState: UploadState = 'idle';
  private _lockTimeout: NodeJS.Timeout | null = null;
  private _uploadCallbacks: Array<(result: UploadResult) => void> = [];

  // Queue system
  private _uploadQueue: UploadQueueItem[] = [];
  private _activeUploads: Map<string, UploadProgress> = new Map();
  private _isProcessingQueue: boolean = false;
  private _maxConcurrentUploads: number = 1; // Prevent multiple simultaneous uploads

  // Memory management
  private _memoryCleanupInterval: NodeJS.Timeout | null = null;
  private _appStateSubscription: any = null;

  // Progress tracking
  private _progressCallbacks: Map<string, (progress: UploadProgress) => void> = new Map();

  constructor() {
    this.initializeManager();
  }

  // Initialize the upload manager
  private initializeManager() {
    // Set up memory cleanup interval (every 5 minutes)
    this._memoryCleanupInterval = setInterval(() => {
      this.cleanupMemory();
    }, 5 * 60 * 1000);

    // Set up app state listener for proper cleanup
    this._appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }

  // Handle app state changes
  private handleAppStateChange(nextAppState: AppStateStatus) {
    if (nextAppState === 'background') {
      // App going to background - pause queue processing
      this._isProcessingQueue = false;
    } else if (nextAppState === 'active') {
      // App coming to foreground - resume queue processing
      if (this._uploadQueue.length > 0 && !this._isProcessingQueue) {
        this.processQueue();
      }
    }
  }

  // Get current upload state
  get currentState(): UploadState {
    return this._currentState;
  }

  // Check if currently uploading
  get isUploading(): boolean {
    return this._isUploading || this._activeUploads.size > 0;
  }

  // Get queue status
  get queueStatus() {
    return {
      queueLength: this._uploadQueue.length,
      activeUploads: this._activeUploads.size,
      isProcessing: this._isProcessingQueue
    };
  }

  // Set the current state
  private setState(state: UploadState) {
    console.log(`[UploadManager] State changed: ${this._currentState} -> ${state}`);
    this._currentState = state;
  }

  // Lock the upload process to prevent multiple simultaneous uploads
  private lock(): boolean {
    if (this._isUploading) {
      console.log('[UploadManager] Upload already in progress, ignoring request');
      return false;
    }

    console.log('[UploadManager] Locking upload process');
    this._isUploading = true;

    // Set a safety timeout to release the lock after 30 seconds
    // This prevents the system from getting stuck if something goes wrong
    this._lockTimeout = setTimeout(() => {
      console.log('[UploadManager] Safety timeout triggered, releasing lock');
      this.unlock();
      this.notifyCallbacks({
        success: false,
        error: 'Upload timed out'
      });
    }, 30000);

    return true;
  }

  // Unlock the upload process
  private unlock() {
    console.log('[UploadManager] Unlocking upload process');
    this._isUploading = false;
    this.setState('idle');

    // Clear the safety timeout
    if (this._lockTimeout) {
      clearTimeout(this._lockTimeout);
      this._lockTimeout = null;
    }
  }

  // Notify all registered callbacks
  private notifyCallbacks(result: UploadResult) {
    // Make a copy of the callbacks array before iterating
    const callbacks = [...this._uploadCallbacks];

    // Clear the callbacks array
    this._uploadCallbacks = [];

    // Notify all callbacks
    callbacks.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        console.error('[UploadManager] Error in callback:', error);
      }
    });
  }

  // Memory cleanup
  private cleanupMemory() {
    console.log('[UploadManager] Running memory cleanup');

    // Clear completed uploads older than 10 minutes
    const tenMinutesAgo = Date.now() - (10 * 60 * 1000);

    // Clean up old progress callbacks
    for (const [uploadId, progress] of this._activeUploads.entries()) {
      if (progress.state === 'success' || progress.state === 'error') {
        if (Date.now() - tenMinutesAgo > 0) {
          this._activeUploads.delete(uploadId);
          this._progressCallbacks.delete(uploadId);
        }
      }
    }

    // Clean up old queue items (older than 30 minutes)
    const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
    this._uploadQueue = this._uploadQueue.filter(item => item.timestamp > thirtyMinutesAgo);

    console.log(`[UploadManager] Cleanup complete. Queue: ${this._uploadQueue.length}, Active: ${this._activeUploads.size}`);
  }

  // Add upload to queue
  public queueUpload(
    type: 'image' | 'document',
    documentType: string,
    documentCategory: DocumentCategory,
    callback: (result: UploadResult) => void,
    useCamera?: boolean,
    maxRetries: number = 3
  ): string {
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const queueItem: UploadQueueItem = {
      id: uploadId,
      type,
      useCamera: useCamera || false,
      documentType,
      documentCategory,
      callback,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries
    };

    this._uploadQueue.push(queueItem);
    console.log(`[UploadManager] Added upload to queue: ${uploadId}, Queue length: ${this._uploadQueue.length}`);

    // Start processing queue if not already processing
    if (!this._isProcessingQueue) {
      this.processQueue();
    }

    return uploadId;
  }

  // Process upload queue
  private async processQueue() {
    if (this._isProcessingQueue || this._uploadQueue.length === 0) {
      return;
    }

    if (this._activeUploads.size >= this._maxConcurrentUploads) {
      console.log('[UploadManager] Max concurrent uploads reached, waiting...');
      return;
    }

    this._isProcessingQueue = true;
    console.log('[UploadManager] Starting queue processing');

    while (this._uploadQueue.length > 0 && this._activeUploads.size < this._maxConcurrentUploads) {
      const queueItem = this._uploadQueue.shift();
      if (!queueItem) break;

      // Start the upload
      this.processUploadItem(queueItem);
    }

    this._isProcessingQueue = false;
  }

  // Process individual upload item
  private async processUploadItem(queueItem: UploadQueueItem) {
    const { id, type, useCamera, documentType, documentCategory, callback } = queueItem;

    // Track upload progress
    const progress: UploadProgress = {
      uploadId: id,
      state: 'preparing',
      progress: 0
    };

    this._activeUploads.set(id, progress);
    this.notifyProgress(id, progress);

    try {
      let result: UploadResult;

      if (type === 'image') {
        result = await this.processImageUpload(id, useCamera || false, documentType, documentCategory);
      } else {
        result = await this.processDocumentUpload(id, documentType, documentCategory);
      }

      // Add upload ID to result
      result.uploadId = id;

      // Notify callback
      callback(result);

      // Update progress
      progress.state = result.success ? 'success' : 'error';
      progress.progress = 100;
      progress.error = result.error;
      this._activeUploads.set(id, progress);
      this.notifyProgress(id, progress);

    } catch (error) {
      console.error(`[UploadManager] Error processing upload ${id}:`, error);

      // Handle retry logic
      if (queueItem.retryCount < queueItem.maxRetries) {
        queueItem.retryCount++;
        console.log(`[UploadManager] Retrying upload ${id} (attempt ${queueItem.retryCount}/${queueItem.maxRetries})`);

        // Add back to queue with delay
        setTimeout(() => {
          this._uploadQueue.unshift(queueItem);
          this.processQueue();
        }, 2000 * queueItem.retryCount); // Exponential backoff
      } else {
        // Max retries reached
        const result: UploadResult = {
          success: false,
          error: `Upload failed after ${queueItem.maxRetries} attempts`,
          uploadId: id
        };

        callback(result);

        progress.state = 'error';
        progress.progress = 0;
        progress.error = result.error;
        this._activeUploads.set(id, progress);
        this.notifyProgress(id, progress);
      }
    } finally {
      // Continue processing queue
      setTimeout(() => {
        this.processQueue();
      }, 100);
    }
  }

  // Register progress callback
  public registerProgressCallback(uploadId: string, callback: (progress: UploadProgress) => void) {
    this._progressCallbacks.set(uploadId, callback);
  }

  // Notify progress
  private notifyProgress(uploadId: string, progress: UploadProgress) {
    const callback = this._progressCallbacks.get(uploadId);
    if (callback) {
      try {
        callback(progress);
      } catch (error) {
        console.error('[UploadManager] Error in progress callback:', error);
      }
    }
  }

  // Register a callback for the current upload (legacy support)
  public registerCallback(callback: (result: UploadResult) => void) {
    this._uploadCallbacks.push(callback);
  }

  // Process image upload (internal method)
  private async processImageUpload(
    uploadId: string,
    useCamera: boolean,
    documentType: string,
    documentCategory: DocumentCategory
  ): Promise<UploadResult> {
    console.log(`[UploadManager] Processing image upload ${uploadId}, useCamera: ${useCamera}`);

    // Update progress
    const progress = this._activeUploads.get(uploadId);
    if (progress) {
      progress.state = 'preparing';
      progress.progress = 10;
      this.notifyProgress(uploadId, progress);
    }

    try {
      // Request permissions
      let permissionResult;
      if (useCamera) {
        permissionResult = await ImagePicker.requestCameraPermissionsAsync();
        if (permissionResult.granted === false) {
          Toast.show({
            type: 'error',
            text1: 'Permission Required',
            text2: 'Camera permission is needed to take photos',
            visibilityTime: 3000,
          });
          return {
            success: false,
            error: 'Camera permission denied'
          };
        }
      } else {
        permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (permissionResult.granted === false) {
          Toast.show({
            type: 'error',
            text1: 'Permission Required',
            text2: 'Media library permission is needed to select photos',
            visibilityTime: 3000,
          });
          return {
            success: false,
            error: 'Media library permission denied'
          };
        }
      }

      // Update progress
      if (progress) {
        progress.state = 'selecting';
        progress.progress = 30;
        this.notifyProgress(uploadId, progress);
      }

      // Launch camera or image picker
      const result = useCamera
        ? await ImagePicker.launchCameraAsync({
            mediaTypes: 'images',
            allowsEditing: true,
            quality: 0.8,
          })
        : await ImagePicker.launchImageLibraryAsync({
            mediaTypes: 'images',
            allowsEditing: true,
            quality: 0.8,
          });

      if (result.canceled) {
        return {
          success: false,
          error: 'User canceled'
        };
      }

      // Update progress
      if (progress) {
        progress.state = 'processing';
        progress.progress = 70;
        this.notifyProgress(uploadId, progress);
      }

      // Get the URI from the result
      const uri = result.assets[0].uri;
      const documentFileType: DocumentFileType = 'image';

      // Create a new document object
      const newDocument: Document = {
        id: `${uploadId}_${Date.now()}`,
        name: documentType,
        type: documentCategory,
        status: 'pending',
        date: new Date().toISOString().split('T')[0],
        uri: uri,
        fileUrl: uri,
        fileName: result.assets[0].fileName || `image_${Date.now()}.jpg`,
        fileType: documentFileType,
        isImage: true,
        fileSize: result.assets[0].fileSize,
        metadata: { timestamp: Date.now(), uploadId }
      };

      // Update progress
      if (progress) {
        progress.state = 'success';
        progress.progress = 100;
        this.notifyProgress(uploadId, progress);
      }

      return {
        success: true,
        document: newDocument
      };
    } catch (error) {
      console.error(`[UploadManager] Error processing image upload ${uploadId}:`, error);

      // Update progress
      if (progress) {
        progress.state = 'error';
        progress.progress = 0;
        progress.error = error instanceof Error ? error.message : 'Failed to pick image';
        this.notifyProgress(uploadId, progress);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to pick image'
      };
    }
  }

  // Process document upload (internal method)
  private async processDocumentUpload(
    uploadId: string,
    documentType: string,
    documentCategory: DocumentCategory
  ): Promise<UploadResult> {
    console.log(`[UploadManager] Processing document upload ${uploadId}`);

    // Update progress
    const progress = this._activeUploads.get(uploadId);
    if (progress) {
      progress.state = 'preparing';
      progress.progress = 10;
      this.notifyProgress(uploadId, progress);
    }

    try {
      // Update progress
      if (progress) {
        progress.state = 'selecting';
        progress.progress = 30;
        this.notifyProgress(uploadId, progress);
      }

      // Launch document picker
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/pdf',
          'image/*',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/rtf',
          'text/plain',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          '*/*'  // Allow all file types as fallback
        ],
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return {
          success: false,
          error: 'User canceled'
        };
      }

      // Update progress
      if (progress) {
        progress.state = 'processing';
        progress.progress = 70;
        this.notifyProgress(uploadId, progress);
      }

      // Determine file type
      let fileType: DocumentFileType = 'other';
      const mimeType = result.assets[0].mimeType?.toLowerCase() || '';

      if (mimeType.includes('pdf')) {
        fileType = 'pdf';
      } else if (mimeType.includes('image')) {
        fileType = 'image';
      } else if (mimeType.includes('word') || mimeType.includes('document')) {
        fileType = 'doc';
      } else if (mimeType.includes('excel') || mimeType.includes('sheet')) {
        fileType = 'excel';
      } else if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) {
        fileType = 'powerpoint';
      } else if (mimeType.includes('text/plain')) {
        fileType = 'text';
      } else if (mimeType.includes('rtf')) {
        fileType = 'rtf';
      }

      // Also check file extension as fallback
      const fileName = result.assets[0].name?.toLowerCase() || '';
      if (fileType === 'other') {
        if (fileName.endsWith('.pdf')) fileType = 'pdf';
        else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) fileType = 'doc';
        else if (fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) fileType = 'excel';
        else if (fileName.endsWith('.ppt') || fileName.endsWith('.pptx')) fileType = 'powerpoint';
        else if (fileName.endsWith('.txt')) fileType = 'text';
        else if (fileName.endsWith('.rtf')) fileType = 'rtf';
        else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') ||
                fileName.endsWith('.png') || fileName.endsWith('.gif')) fileType = 'image';
      }

      // Get the URI from the result
      const uri = result.assets[0].uri;

      // Create a new document object
      const newDocument: Document = {
        id: `${uploadId}_${Date.now()}`,
        name: documentType,
        type: documentCategory,
        status: 'pending',
        date: new Date().toISOString().split('T')[0],
        uri: uri,
        fileUrl: uri,
        fileName: result.assets[0].name,
        fileType: fileType,
        isImage: fileType === 'image',
        fileSize: result.assets[0].size,
        metadata: { timestamp: Date.now(), uploadId }
      };

      // Update progress
      if (progress) {
        progress.state = 'success';
        progress.progress = 100;
        this.notifyProgress(uploadId, progress);
      }

      return {
        success: true,
        document: newDocument
      };
    } catch (error) {
      console.error(`[UploadManager] Error processing document upload ${uploadId}:`, error);

      // Update progress
      if (progress) {
        progress.state = 'error';
        progress.progress = 0;
        progress.error = error instanceof Error ? error.message : 'Failed to pick document';
        this.notifyProgress(uploadId, progress);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to pick document'
      };
    }
  }

  // Public API methods (new queue-based approach)
  public pickImage(
    useCamera: boolean,
    documentType: string,
    documentCategory: DocumentCategory,
    callback: (result: UploadResult) => void
  ): string {
    return this.queueUpload('image', documentType, documentCategory, callback, useCamera);
  }

  public pickDocument(
    documentType: string,
    documentCategory: DocumentCategory,
    callback: (result: UploadResult) => void
  ): string {
    return this.queueUpload('document', documentType, documentCategory, callback);
  }

  // Cancel upload
  public cancelUpload(uploadId: string): boolean {
    // Remove from queue if not started
    const queueIndex = this._uploadQueue.findIndex(item => item.id === uploadId);
    if (queueIndex !== -1) {
      this._uploadQueue.splice(queueIndex, 1);
      console.log(`[UploadManager] Cancelled queued upload: ${uploadId}`);
      return true;
    }

    // Mark active upload as cancelled
    const activeUpload = this._activeUploads.get(uploadId);
    if (activeUpload) {
      activeUpload.state = 'error';
      activeUpload.error = 'Cancelled by user';
      this.notifyProgress(uploadId, activeUpload);
      console.log(`[UploadManager] Cancelled active upload: ${uploadId}`);
      return true;
    }

    return false;
  }

  // Get upload progress
  public getUploadProgress(uploadId: string): UploadProgress | null {
    return this._activeUploads.get(uploadId) || null;
  }

  // Clear completed uploads
  public clearCompletedUploads(): void {
    for (const [uploadId, progress] of this._activeUploads.entries()) {
      if (progress.state === 'success' || progress.state === 'error') {
        this._activeUploads.delete(uploadId);
        this._progressCallbacks.delete(uploadId);
      }
    }
  }

  // Cleanup and destroy
  public destroy(): void {
    console.log('[UploadManager] Destroying upload manager');

    // Clear all timeouts and intervals
    if (this._lockTimeout) {
      clearTimeout(this._lockTimeout);
      this._lockTimeout = null;
    }

    if (this._memoryCleanupInterval) {
      clearInterval(this._memoryCleanupInterval);
      this._memoryCleanupInterval = null;
    }

    // Remove app state listener
    if (this._appStateSubscription) {
      this._appStateSubscription.remove();
      this._appStateSubscription = null;
    }

    // Clear all data
    this._uploadQueue = [];
    this._activeUploads.clear();
    this._progressCallbacks.clear();
    this._uploadCallbacks = [];

    // Reset state
    this._isUploading = false;
    this._isProcessingQueue = false;
    this._currentState = 'idle';
  }
}

// Create a singleton instance
export const UploadManager = new UploadManagerClass();
