# Testing Guide for Inerca Holdings Mobile Application

## Overview

This document provides comprehensive information about the testing setup, structure, and execution for the Inerca Holdings mobile application.

## Testing Stack

- **Test Runner**: Jest with jest-expo preset
- **Component Testing**: React Native Testing Library
- **Mocking**: Jest mocks for React Native modules
- **Coverage**: Istanbul/NYC for code coverage reporting
- **CI/CD**: GitHub Actions compatible

## Test Structure

```
__tests__/
├── unit/                    # Unit tests
│   ├── redux/              # Redux slice tests
│   ├── utils/              # Utility function tests
│   ├── hooks/              # Custom hook tests
│   └── validation/         # Validation logic tests
├── integration/            # Integration tests
│   ├── api/               # API service tests
│   ├── auth/              # Authentication flow tests
│   ├── upload/            # File upload tests
│   └── navigation/        # Navigation tests
├── component/             # Component tests
│   ├── forms/             # Form component tests
│   ├── screens/           # Screen component tests
│   └── common/            # Common component tests
└── e2e/                   # End-to-end tests
    ├── user-flows/        # Complete user journey tests
    ├── cross-platform/    # Platform-specific tests
    └── performance/       # Performance tests
```

## Available Test Commands

### Basic Commands
```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run all tests with verbose output and coverage
npm run test:all

# Watch mode for development
npm run test:watch

# CI mode (no watch, with coverage)
npm run test:ci
```

### Specific Test Types
```bash
# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run only component tests
npm run test:component

# Run only end-to-end tests
npm run test:e2e
```

## Coverage Requirements

### Global Coverage Thresholds
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Critical Path Requirements
- **Authentication flows**: 100% coverage
- **Payment processing**: 100% coverage
- **Data validation**: 90% coverage
- **API services**: 85% coverage

## Test Categories

### 1. Unit Tests

#### Redux Slices (`__tests__/unit/redux/`)
- State management logic
- Action creators
- Reducers
- Async thunks

**Example**: `authSlice.test.ts`
```typescript
describe('authSlice', () => {
  it('should handle login success', async () => {
    const result = await store.dispatch(login({ email, password }));
    expect(login.fulfilled.match(result)).toBe(true);
  });
});
```

#### Utility Functions (`__tests__/unit/utils/`)
- Quote calculations
- Data transformations
- Validation functions
- Helper utilities

**Example**: `quoteCalculations.test.ts`
```typescript
describe('calculateMotorPremium', () => {
  it('should calculate Toyota premium correctly', () => {
    const result = calculateMotorPremium(100000, 'Toyota', false);
    expect(result.premium).toBe(3000);
  });
});
```

### 2. Integration Tests

#### API Services (`__tests__/integration/api/`)
- HTTP request/response handling
- Error handling
- Authentication flows
- Data serialization

**Example**: `authService.test.ts`
```typescript
describe('Auth API Integration', () => {
  it('should login with valid credentials', async () => {
    const result = await apiService.auth.login(email, password);
    expect(result.access_token).toBeDefined();
  });
});
```

### 3. Component Tests

#### React Components (`__tests__/component/`)
- Component rendering
- User interactions
- Props handling
- State changes

**Example**: `PolicyAgreementComponent.test.tsx`
```typescript
describe('PolicyAgreementComponent', () => {
  it('should enable checkbox after scrolling', async () => {
    fireEvent.scroll(scrollView, scrollToBottomEvent);
    await waitFor(() => {
      fireEvent.press(checkbox);
      expect(mockOnAgreementChange).toHaveBeenCalledWith(true);
    });
  });
});
```

### 4. End-to-End Tests

#### User Flows (`__tests__/e2e/user-flows/`)
- Complete user journeys
- Multi-step processes
- Cross-component interactions
- State persistence

**Example**: `authFlow.test.ts`
```typescript
describe('Complete Registration Flow', () => {
  it('should handle user registration to login', async () => {
    await store.dispatch(register(userData));
    expect(store.getState().auth.isAuthenticated).toBe(true);
  });
});
```

## Mocking Strategy

### Global Mocks (`jest.setup.js`)
- React Native modules
- Expo modules
- AsyncStorage
- Navigation
- External libraries

### Test-Specific Mocks
```typescript
// Mock API service
jest.mock('../../../services/api', () => ({
  apiService: {
    auth: {
      login: jest.fn(),
      register: jest.fn(),
    },
  },
}));
```

## Best Practices

### 1. Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Clean up after each test

### 2. Mocking Guidelines
- Mock external dependencies
- Use real implementations for internal logic
- Reset mocks between tests
- Verify mock calls when relevant

### 3. Async Testing
```typescript
// Use async/await for async operations
it('should handle async operation', async () => {
  const result = await asyncFunction();
  expect(result).toBeDefined();
});

// Use waitFor for UI updates
await waitFor(() => {
  expect(getByText('Success')).toBeTruthy();
});
```

### 4. Error Testing
```typescript
// Test error scenarios
it('should handle API errors', async () => {
  apiService.auth.login.mockRejectedValue(new Error('Network error'));
  await expect(login()).rejects.toThrow('Network error');
});
```

## Coverage Reports

### HTML Report
After running `npm run test:coverage`, open `coverage/lcov-report/index.html` in your browser.

### Console Output
```bash
npm run test:coverage
```

### Coverage Files
- `coverage/lcov.info` - LCOV format for CI/CD
- `coverage/coverage-final.json` - JSON format
- `coverage/lcov-report/` - HTML report

## Continuous Integration

### GitHub Actions Example
```yaml
- name: Run Tests
  run: npm run test:ci

- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage/lcov.info
```

## Debugging Tests

### Debug Mode
```bash
# Run specific test file
npm test -- authSlice.test.ts

# Run with debug output
npm test -- --verbose

# Run single test
npm test -- --testNamePattern="should login successfully"
```

### VS Code Debugging
1. Set breakpoints in test files
2. Use "Jest Debug" configuration
3. Run specific test in debug mode

## Performance Testing

### Memory Leaks
```typescript
describe('Memory Management', () => {
  it('should not leak memory during upload', () => {
    // Test memory usage patterns
  });
});
```

### Render Performance
```typescript
describe('Component Performance', () => {
  it('should render within acceptable time', () => {
    const startTime = performance.now();
    render(<Component />);
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(100);
  });
});
```

## Test Data Management

### Mock Data
```typescript
// Create reusable mock data
export const mockUser = {
  id: '123',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
};
```

### Test Utilities
```typescript
// Create test utilities
export const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: { auth: authSlice },
    preloadedState: initialState,
  });
};
```

## Troubleshooting

### Common Issues

1. **Module not found errors**
   - Check jest.setup.js for missing mocks
   - Verify import paths

2. **Async test timeouts**
   - Increase timeout with `jest.setTimeout(10000)`
   - Use proper async/await patterns

3. **React Native component errors**
   - Ensure all RN components are mocked
   - Check for missing native module mocks

### Debug Commands
```bash
# Clear Jest cache
npx jest --clearCache

# Run with no cache
npm test -- --no-cache

# Show test configuration
npx jest --showConfig
```

## Contributing

### Adding New Tests
1. Follow the established directory structure
2. Use descriptive test names
3. Include both success and failure scenarios
4. Maintain coverage thresholds
5. Update this documentation if needed

### Test Review Checklist
- [ ] Tests cover both happy path and edge cases
- [ ] Mocks are properly configured
- [ ] Async operations are handled correctly
- [ ] Coverage thresholds are maintained
- [ ] Tests are deterministic and don't rely on external state
