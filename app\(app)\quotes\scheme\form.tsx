import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { User, Calendar, Briefcase, Building, FileText } from 'lucide-react-native';
import PulaIcon from '@/components/ui/PulaIcon';
import QuoteFormContainer from '@/components/quotes/QuoteFormContainer';
import DynamicFormField, { SelectOption } from '@/components/quotes/DynamicFormField';
import QuoteDocumentsSection from '@/components/quotes/QuoteDocumentsSection';
import useQuoteStore from '@/store/quoteStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import { QuoteDocument } from '@/types/quote.types';

export default function SchemeQuoteForm() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);

  // Get params
  const params = useLocalSearchParams();
  const quoteId = params.quoteId as string;

  // Get quote store functions
  const {
    getQuoteById,
    updateQuote,
    setCurrentQuote,
    currentQuote,
    isLoading: isQuoteLoading
  } = useQuoteStore();

  // Form state
  const [formData, setFormData] = useState({
    memberDetails: {
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      employeeNumber: '',
    },
    employerName: '',
    employerAddress: '',
    schemeType: '',
  });

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formValid, setFormValid] = useState(false);

  // Documents state
  const [documents, setDocuments] = useState<QuoteDocument[]>([]);
  const [activeTab, setActiveTab] = useState<'details' | 'documents'>('details');

  // Load quote data
  useEffect(() => {
    if (quoteId) {
      const quote = getQuoteById(quoteId as string);
      if (quote) {
        setCurrentQuote(quote);

        // Populate form with additional info if available
        if (quote.additionalInfo) {
          setFormData(prevData => ({
            ...prevData,
            ...(quote.additionalInfo as any),
          }));
        }

        // Set documents if available
        if (quote.documents) {
          setDocuments(quote.documents);
        } else {
          // Initialize with required documents
          setDocuments([
            {
              id: Date.now().toString() + '1',
              name: 'Supporting Personal Insurance Forms',
              type: 'supporting_forms',
              required: true,
              uploaded: false,
            },
          ]);
        }
      }
    }
  }, [quoteId, getQuoteById, setCurrentQuote]);

  // Update form field
  const updateFormField = (field: string, value: any) => {
    // Handle nested fields
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Validate form
  useEffect(() => {
    validateForm();
  }, [formData]);

  // Validate form fields
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate member details
    if (!formData.memberDetails.firstName) {
      newErrors['memberDetails.firstName'] = 'First name is required';
    }

    if (!formData.memberDetails.lastName) {
      newErrors['memberDetails.lastName'] = 'Last name is required';
    }

    if (!formData.memberDetails.employeeNumber) {
      newErrors['memberDetails.employeeNumber'] = 'Employee number is required';
    }

    // Validate employer name
    if (!formData.employerName) {
      newErrors.employerName = 'Employer name is required';
    }

    // Validate scheme type
    if (!formData.schemeType) {
      newErrors.schemeType = 'Scheme type is required';
    }

    setErrors(newErrors);
    setFormValid(Object.keys(newErrors).length === 0);
  };

  // Handle documents updated
  const handleDocumentsUpdated = (updatedDocuments: QuoteDocument[]) => {
    setDocuments(updatedDocuments);
  };

  // Handle back button
  const handleBack = () => {
    router.back();
  };

  // Handle save button
  const handleSave = async () => {
    try {
      // Update the quote with the current form data and documents
      await updateQuote({
        additionalInfo: formData,
        documents: documents,
        // Make sure the status is still draft
        status: 'draft',
        // Update the timestamp to ensure it's at the top of the list
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Show a toast notification for better user feedback
      showToast(
        'success',
        'Draft Saved',
        'Your quote draft has been saved',
        { visibilityTime: 2000 }
      );

      // Navigate back to quotes list
      router.push('/(app)/quotes');
    } catch (error) {
      console.error('Error saving quote:', error);
      showToast(
        'error',
        'Error',
        'Failed to save quote. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Handle next button
  const handleNext = async () => {
    if (!currentQuote) return;

    try {
      // Calculate a simple premium (in a real app, this would be more complex)
      const premium = 1500; // Fixed premium for scheme insurance
      
      // Save form data and documents
      await updateQuote({
        additionalInfo: formData,
        documents: documents,
        premium: premium,
        currency: 'P', // Set Botswana currency
        coverAmount: 500000, // Standard cover amount for scheme
        // Update the timestamp
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Navigate to summary page
      router.push(`/quotes/${currentQuote.type}/summary?quoteId=${currentQuote.id}`);
    } catch (error) {
      console.error('Error saving quote:', error);
      showToast(
        'error',
        'Error',
        'Failed to save quote. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Define form steps
  const steps = [
    { id: '1', title: 'Client Info' },
    { id: '2', title: 'Scheme Details' },
    { id: '3', title: 'Documents' },
    { id: '4', title: 'Review' },
  ];

  // Render form fields
  const renderFormFields = () => {
    return (
      <View>
        <Text style={styles.sectionTitle}>Member Details</Text>
        
        <DynamicFormField
          label="First Name"
          type="text"
          value={formData.memberDetails.firstName}
          onChange={(value) => updateFormField('memberDetails.firstName', value)}
          placeholder="Enter member's first name"
          error={errors['memberDetails.firstName']}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Last Name"
          type="text"
          value={formData.memberDetails.lastName}
          onChange={(value) => updateFormField('memberDetails.lastName', value)}
          placeholder="Enter member's last name"
          error={errors['memberDetails.lastName']}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Date of Birth"
          type="date"
          value={formData.memberDetails.dateOfBirth}
          onChange={(value) => updateFormField('memberDetails.dateOfBirth', value)}
          placeholder="Select date of birth"
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Employee Number"
          type="text"
          value={formData.memberDetails.employeeNumber}
          onChange={(value) => updateFormField('memberDetails.employeeNumber', value)}
          placeholder="Enter employee number"
          error={errors['memberDetails.employeeNumber']}
          required
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Employer Details</Text>

        <DynamicFormField
          label="Employer Name"
          type="text"
          value={formData.employerName}
          onChange={(value) => updateFormField('employerName', value)}
          placeholder="Enter employer name"
          error={errors.employerName}
          required
          icon={<Building size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Employer Address"
          type="text"
          value={formData.employerAddress}
          onChange={(value) => updateFormField('employerAddress', value)}
          placeholder="Enter employer address"
          icon={<Building size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Scheme Type"
          type="select"
          value={formData.schemeType}
          onChange={(value) => updateFormField('schemeType', value)}
          placeholder="Select scheme type"
          error={errors.schemeType}
          required
          options={[
            { value: 'health', label: 'Health Insurance' },
            { value: 'life', label: 'Life Assurance' },
            { value: 'pension', label: 'Pension' },
            { value: 'combined', label: 'Combined Benefits' },
          ]}
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />
      </View>
    );
  };

  return (
    <QuoteFormContainer
      title="Scheme Insurance Details"
      subtitle="Please provide details for your scheme insurance quote"
      steps={steps}
      currentStep={1}
      completedSteps={[0]}
      onBack={handleBack}
      onNext={handleNext}
      onSave={handleSave}
      isLoading={isQuoteLoading}
      nextDisabled={!formValid}
    >
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'details' && styles.activeTab
          ]}
          onPress={() => setActiveTab('details')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'details' && styles.activeTabText
          ]}>
            Scheme Details
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'documents' && styles.activeTab
          ]}
          onPress={() => setActiveTab('documents')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'documents' && styles.activeTabText
          ]}>
            Required Documents
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        {/* Render both components but only show the active one */}
        <View style={{ display: activeTab === 'details' ? 'flex' : 'none' }}>
          {renderFormFields()}
        </View>

        <View style={{ display: activeTab === 'documents' ? 'flex' : 'none' }}>
          <QuoteDocumentsSection
            quoteType="scheme"
            documents={documents}
            onDocumentsUpdated={handleDocumentsUpdated}
          />
        </View>
      </View>
    </QuoteFormContainer>
  );
}

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#4C6FFF',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#4C6FFF',
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
});
