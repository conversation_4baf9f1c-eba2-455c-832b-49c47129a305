import { configureStore } from '@reduxjs/toolkit';
import authSlice, { login, register, logout, forgotPassword, resetPassword } from '../../../store/authSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock the entire API service
jest.mock('../../../services/api', () => ({
  apiService: {
    auth: {
      login: jest.fn(),
      register: jest.fn(),
      getCurrentUser: jest.fn(),
      forgotPassword: jest.fn(),
      resetPassword: jest.fn(),
    },
  },
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage');

describe('Authentication Flow E2E Tests', () => {
  let store: any;
  const { apiService } = require('../../../services/api');

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authSlice,
      },
    });
    jest.clearAllMocks();
  });

  describe('Complete Registration Flow', () => {
    it('should handle complete user registration flow', async () => {
      // Mock successful registration
      const mockUserResponse = {
        id: '123',
        email: '<EMAIL>',
        username: 'New User',
        phone_number: '+2671234567',
        is_active: true,
      };

      const mockLoginResponse = {
        access_token: 'mock-token',
        token_type: 'bearer',
      };

      const mockCurrentUser = {
        id: '123',
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        phone: '+2671234567',
        address: '123 Test St',
        profileImage: '',
        isEmailVerified: true,
        isPhoneVerified: false,
        userType: 'individual',
        role: 'user',
        idNumber: '123456789',
        occupation: 'Tester',
      };

      apiService.auth.register.mockResolvedValue(mockUserResponse);
      apiService.auth.login.mockResolvedValue(mockLoginResponse);
      apiService.auth.getCurrentUser.mockResolvedValue(mockCurrentUser);

      // Step 1: Register user
      const registerResult = await store.dispatch(register({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        phone: '+2671234567',
        userType: 'individual',
      }));

      expect(register.fulfilled.match(registerResult)).toBe(true);

      // Verify registration state
      let state = store.getState().auth;
      expect(state.user).toEqual(mockCurrentUser);
      expect(state.isAuthenticated).toBe(true);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();

      // Verify API calls
      expect(apiService.auth.register).toHaveBeenCalledWith({
        email: '<EMAIL>',
        hashed_password: expect.any(String),
        username: 'New User',
        phone_number: '+2671234567',
        role: 'user',
      });

      expect(apiService.auth.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(apiService.auth.getCurrentUser).toHaveBeenCalled();

      // Verify storage calls
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('token', 'mock-token');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockCurrentUser));
    });

    it('should handle registration failure gracefully', async () => {
      apiService.auth.register.mockRejectedValue(new Error('Email already exists'));

      const registerResult = await store.dispatch(register({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Existing',
        lastName: 'User',
        phone: '+2671234567',
        userType: 'individual',
      }));

      expect(register.rejected.match(registerResult)).toBe(true);

      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('Email already exists');
    });
  });

  describe('Complete Login Flow', () => {
    it('should handle complete user login flow', async () => {
      const mockLoginResponse = {
        access_token: 'login-token',
        token_type: 'bearer',
      };

      const mockCurrentUser = {
        id: '456',
        email: '<EMAIL>',
        firstName: 'Existing',
        lastName: 'User',
        phone: '+2671234567',
        address: '456 Test Ave',
        profileImage: '',
        isEmailVerified: true,
        isPhoneVerified: true,
        userType: 'individual',
        role: 'user',
        idNumber: '987654321',
        occupation: 'Manager',
      };

      apiService.auth.login.mockResolvedValue(mockLoginResponse);
      apiService.auth.getCurrentUser.mockResolvedValue(mockCurrentUser);

      const loginResult = await store.dispatch(login({
        email: '<EMAIL>',
        password: 'password123',
      }));

      expect(login.fulfilled.match(loginResult)).toBe(true);

      const state = store.getState().auth;
      expect(state.user).toEqual(mockCurrentUser);
      expect(state.isAuthenticated).toBe(true);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();

      expect(apiService.auth.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(apiService.auth.getCurrentUser).toHaveBeenCalled();
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('token', 'login-token');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockCurrentUser));
    });

    it('should handle login failure with invalid credentials', async () => {
      apiService.auth.login.mockRejectedValue(new Error('Invalid credentials'));

      const loginResult = await store.dispatch(login({
        email: '<EMAIL>',
        password: 'wrongpassword',
      }));

      expect(login.rejected.match(loginResult)).toBe(true);

      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('Invalid credentials');
    });
  });

  describe('Complete Password Reset Flow', () => {
    it('should handle complete password reset flow', async () => {
      apiService.auth.forgotPassword.mockResolvedValue(undefined);
      apiService.auth.resetPassword.mockResolvedValue(undefined);

      // Step 1: Request password reset
      const forgotResult = await store.dispatch(forgotPassword('<EMAIL>'));

      expect(forgotPassword.fulfilled.match(forgotResult)).toBe(true);

      let state = store.getState().auth;
      expect(state.resetEmail).toBe('<EMAIL>');
      expect(state.error).toBeNull();

      // Step 2: Reset password with code
      const resetResult = await store.dispatch(resetPassword({
        email: '<EMAIL>',
        code: 123456,
        new_password: 'newpassword123',
      }));

      expect(resetPassword.fulfilled.match(resetResult)).toBe(true);

      state = store.getState().auth;
      expect(state.resetEmail).toBeNull();
      expect(state.error).toBeNull();

      expect(apiService.auth.forgotPassword).toHaveBeenCalledWith('<EMAIL>');
      expect(apiService.auth.resetPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        code: 123456,
        new_password: 'newpassword123',
      });
    });

    it('should handle password reset failure', async () => {
      apiService.auth.forgotPassword.mockResolvedValue(undefined);
      apiService.auth.resetPassword.mockRejectedValue(new Error('Invalid code'));

      // Step 1: Request password reset (success)
      await store.dispatch(forgotPassword('<EMAIL>'));

      // Step 2: Reset password with invalid code (failure)
      const resetResult = await store.dispatch(resetPassword({
        email: '<EMAIL>',
        code: 999999,
        new_password: 'newpassword123',
      }));

      expect(resetPassword.rejected.match(resetResult)).toBe(true);

      const state = store.getState().auth;
      expect(state.error).toBe('Invalid code');
      expect(state.resetEmail).toBe('<EMAIL>'); // Should still be set
    });
  });

  describe('Complete Logout Flow', () => {
    it('should handle complete logout flow', async () => {
      // First login a user
      const mockUser = {
        id: '789',
        email: '<EMAIL>',
        firstName: 'Logged',
        lastName: 'User',
        phone: '+2671234567',
        address: '789 Test Blvd',
        profileImage: '',
        isEmailVerified: true,
        isPhoneVerified: true,
        userType: 'individual',
        role: 'user',
        idNumber: '555666777',
        occupation: 'Developer',
      };

      // Set user as logged in
      store.dispatch({
        type: 'auth/login/fulfilled',
        payload: mockUser,
      });

      expect(store.getState().auth.isAuthenticated).toBe(true);
      expect(store.getState().auth.user).toEqual(mockUser);

      // Now logout
      const logoutResult = await store.dispatch(logout());

      expect(logout.fulfilled.match(logoutResult)).toBe(true);

      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeNull();
      expect(state.resetEmail).toBeNull();

      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('token');
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('user');
    });
  });

  describe('Session Management', () => {
    it('should handle session persistence across app restarts', async () => {
      const mockUser = {
        id: '999',
        email: '<EMAIL>',
        firstName: 'Persistent',
        lastName: 'User',
      };

      // Mock AsyncStorage to return stored data
      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce('stored-token')
        .mockResolvedValueOnce(JSON.stringify(mockUser));

      // Initialize auth (simulating app restart)
      const { initializeAuth } = require('../../../store/authSlice');
      const initResult = await store.dispatch(initializeAuth());

      expect(initializeAuth.fulfilled.match(initResult)).toBe(true);

      const state = store.getState().auth;
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
      expect(state.isLoading).toBe(false);
    });

    it('should handle corrupted session data gracefully', async () => {
      // Mock AsyncStorage to return corrupted data
      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce('stored-token')
        .mockResolvedValueOnce('invalid-json');

      const { initializeAuth } = require('../../../store/authSlice');
      const initResult = await store.dispatch(initializeAuth());

      expect(initializeAuth.rejected.match(initResult)).toBe(true);

      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeTruthy();
    });
  });
});
